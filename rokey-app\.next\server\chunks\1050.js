"use strict";exports.id=1050,exports.ids=[1050],exports.modules={19163:(e,s,t)=>{t.d(s,{RW:()=>r,r0:()=>a,s0:()=>l});let r={google_drive:"Google Drive",google_docs:"Google Docs",google_sheets:"Google Sheets",gmail:"Gmail",calendar:"Google Calendar",youtube:"YouTube",notion:"Notion",supabase:"Supabase"},l={google_drive:"https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg",google_docs:"https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg",google_sheets:"https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg",gmail:"https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg",calendar:"https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg",youtube:"https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg",notion:"https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png",supabase:"https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg"},a={google_drive:"Access and manage Google Drive files",google_docs:"Create and edit Google Documents",google_sheets:"Work with Google Spreadsheets",gmail:"Send and manage emails",calendar:"Manage calendar events and schedules",youtube:"Access YouTube data and analytics",notion:"Access Notion databases and pages",supabase:"Direct database operations"}},71050:(e,s,t)=>{t.d(s,{c_:()=>D});var r=t(60687),l=t(64908),a=t(15022),o=t(59922);function n({data:e,children:s,icon:t,color:l="#ff6b35",hasInput:n=!0,hasOutput:i=!0,hasRoleInput:d=!1,hasToolsInput:c=!1,hasBrowsingInput:x=!1,inputLabel:p="Input",outputLabel:g="Output",roleInputLabel:u="Role",toolsInputLabel:m="Tools",browsingInputLabel:h="Browse",inputHandles:y=[],className:f=""}){let b=e.isConfigured,v=e.hasError;return(0,r.jsxs)("div",{className:`relative ${f}`,children:[n&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"input",className:"w-6 h-6 border-2 border-gray-500 bg-gray-700 hover:border-[#ff6b35] hover:bg-[#ff6b35] transition-colors",style:{left:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-gray-300 font-medium pointer-events-none",style:{left:-50,top:"45%"},children:p})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"role",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"30%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-50,top:"25%"},children:u})]}),c&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"65%"},children:m})]}),x&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"browsing",className:"w-6 h-6 border-2 border-cyan-500 bg-cyan-700 hover:border-cyan-400 hover:bg-cyan-400 transition-colors",style:{left:-12,top:"85%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-cyan-200 font-medium pointer-events-none",style:{left:-50,top:"80%"},children:h})]}),y&&y.length>0&&y.map((e,s)=>(0,r.jsxs)("div",{children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:e.id,className:"w-8 h-8 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors cursor-pointer rounded-full",style:{left:-16,top:0===s?"30%":"60%",zIndex:10}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none whitespace-nowrap",style:{left:-70,top:0===s?"25%":"55%",zIndex:5},children:e.label})]},e.id)),(0,r.jsxs)("div",{className:`min-w-[200px] rounded-lg border-2 transition-all duration-200 ${v?"border-red-500 bg-red-900/20":b?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"} backdrop-blur-sm shadow-lg hover:shadow-xl`,style:{borderColor:v?"#ef4444":b?l:"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:v?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":`linear-gradient(135deg, ${l}20, ${l}10)`},children:[t&&(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:v?"#ef444420":`${l}20`,color:v?"#ef4444":l},children:(0,r.jsx)(t,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:e.label}),e.description&&(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:e.description})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:v?(0,r.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full",title:"Error"}):b?(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Configured"}):(0,r.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full",title:"Needs configuration"})})]}),s&&(0,r.jsx)("div",{className:"px-4 py-3 border-t border-gray-700/50",children:s}),v&&e.errorMessage&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-900/30 border-t border-red-700/50 rounded-b-lg",children:(0,r.jsx)("div",{className:"text-xs text-red-300",children:e.errorMessage})})]}),i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.h7,{type:"source",position:o.yX.Right,className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:g})]})]})}var i=t(68589),d=t(48544);let c={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},x={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var p=t(66524);let g={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},u={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"};var m=t(61245),h=t(93635),y=t(30474),f=t(58089),b=t(59168),v=t(36920),j=t(19163),N=t(74461),w=t(43210),A=t(70143);let k={openai:"#10b981",anthropic:"#f97316",google:"#3b82f6",deepseek:"#8b5cf6",xai:"#374151",openrouter:"linear-gradient(45deg, #ff6b35, #f7931e, #3b82f6, #8b5cf6)"},I={openai:"OpenAI",anthropic:"Anthropic",google:"Google",deepseek:"DeepSeek",xai:"xAI (Grok)",openrouter:"OpenRouter"},D={userRequest:function({data:e}){return(0,r.jsx)(n,{data:e,icon:l.A,color:"#10b981",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Entry point for user input"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node captures the initial user request and starts the workflow execution."})]})})},classifier:function({data:e}){return(0,r.jsx)(n,{data:e,icon:i.A,color:"#3b82f6",hasInput:!0,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI-powered request analysis"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Analyzes the user request to determine the best routing strategy and extract key information."}),e.config?.classifierType&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-blue-900/30 rounded text-xs text-blue-300",children:["Type: ",e.config.classifierType]})]})})},provider:function({data:e,id:s}){let t=(0,a.Yu)(),l=(0,a.pk)(),o=e.config,i=o?.providerId,p=o?.modelId,g=i?c[i]:"#ff6b35",u=i?x[i]:"AI Provider",m=t.filter(e=>e.target===s&&"role"===e.targetHandle).map(e=>{let s=l.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:t?.roleName||s.data.label||"Unknown Role",type:t?.roleType||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(n,{data:e,icon:d.A,color:"string"==typeof g?g:"#ff6b35",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,hasBrowsingInput:!1,children:(0,r.jsx)("div",{className:"space-y-3",children:i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:u}),"openrouter"===i&&(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-blue-500 text-white px-2 py-0.5 rounded-full",children:"300+ Models"})]}),p&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",p]}),o?.parameters&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",o.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",o.parameters.maxTokens||"Auto"]})]}),o?.fallbackProvider&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",x[o.fallbackProvider.providerId]]}),m.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:m.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"AI Provider Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to OpenAI, Anthropic, Google, DeepSeek, xAI, or OpenRouter."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},vision:function({data:e,id:s}){let t=(0,a.Yu)(),l=(0,a.pk)(),o=e.config,i=o?.providerId,d=o?.modelId,c=i?g[i]:"#8b5cf6",x=i?u[i]:"Vision AI",m=t.filter(e=>e.target===s&&"role"===e.targetHandle).map(e=>{let s=l.find(s=>s.id===e.source);if(s&&"roleAgent"===s.type){let t=s.data.config;return{id:e.source,name:t?.roleName||s.data.label||"Unknown Role",type:t?.roleType||"predefined"}}return null}).filter(Boolean);return(0,r.jsx)(n,{data:e,icon:p.A,color:"string"==typeof c?c:"#8b5cf6",hasInput:!1,hasOutput:!0,hasRoleInput:!0,hasToolsInput:!1,children:(0,r.jsx)("div",{className:"space-y-3",children:i?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:x}),(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-0.5 rounded-full",children:"Vision"})]}),d&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",d]}),o?.parameters&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-400",children:["Temp: ",o.parameters.temperature||1]}),(0,r.jsxs)("div",{className:"text-gray-400",children:["Max: ",o.parameters.maxTokens||"Auto"]})]}),o?.fallbackProvider&&(0,r.jsxs)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:["Fallback: ",u[o.fallbackProvider.providerId]]}),m.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Roles:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:m.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Vision AI Connection"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure to connect to multimodal AI models for image analysis and vision tasks."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},output:function({data:e}){return(0,r.jsx)(n,{data:e,icon:m.A,color:"#ef4444",hasInput:!0,hasOutput:!1,children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Final response output"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"This node formats and delivers the final response to the user. Every workflow must end here."}),e.config?.outputFormat&&(0,r.jsxs)("div",{className:"mt-2 px-2 py-1 bg-red-900/30 rounded text-xs text-red-300",children:["Format: ",e.config.outputFormat]})]})})},roleAgent:function({data:e}){let s=e.config,t=s?.roleName,l=s?.tools||[];return(0,r.jsx)(n,{data:e,icon:h.A,color:"#8b5cf6",hasInput:!1,hasOutput:!0,outputLabel:"Role",children:(0,r.jsx)("div",{className:"space-y-3",children:t?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm font-medium text-white",children:t}),s?.customPrompt&&(0,r.jsx)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:"Custom prompt configured"}),l.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Tools:"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,3).map((e,s)=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded",children:e},s)),l.length>3&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:["+",l.length-3," more"]})]})]}),s?.memoryEnabled&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✓ Memory enabled"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"Role Plugin"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to AI Provider nodes to assign specialized roles (e.g., Coder, Writer, Analyst)."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},centralRouter:function({data:e,id:s}){let t=(0,a.Yu)(),l=(0,a.pk)(),n=e.config,d=t.filter(e=>e.target===s&&"providers"===e.targetHandle).map(e=>{let s=l.find(s=>s.id===e.source);if(s&&"provider"===s.type){let t=s.data.config;return{id:e.source,name:t?.providerId||"Unknown Provider",model:t?.modelId||"Unknown Model"}}return null}).filter(Boolean),c=t.filter(e=>e.target===s&&"vision"===e.targetHandle).map(e=>{let s=l.find(s=>s.id===e.source);if(s&&"vision"===s.type){let t=s.data.config;return{id:e.source,name:t?.providerId||"Unknown Vision Provider",model:t?.modelId||"Unknown Model"}}return null}).filter(Boolean),x=t.filter(e=>e.target===s&&"tools"===e.targetHandle).map(e=>{let s=l.find(s=>s.id===e.source);if(s&&"tool"===s.type){let t=s.data.config;return{id:e.source,name:t?.toolType||"Unknown Tool",status:t?.connectionStatus||"disconnected"}}return null}).filter(Boolean),p=t.some(e=>e.target===s&&"classifier"===e.targetHandle&&l.find(s=>s.id===e.source&&"classifier"===s.type));return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"providers",className:"w-6 h-6 border-2 border-blue-500 bg-blue-700 hover:border-blue-400 hover:bg-blue-400 transition-colors",style:{left:-12,top:"20%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-blue-200 font-medium pointer-events-none",style:{left:-80,top:"15%"},children:"AI Providers"}),(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"vision",className:"w-6 h-6 border-2 border-purple-500 bg-purple-700 hover:border-purple-400 hover:bg-purple-400 transition-colors",style:{left:-12,top:"35%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-purple-200 font-medium pointer-events-none",style:{left:-80,top:"30%"},children:"Vision Models"}),(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"classifier",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"50%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-80,top:"45%"},children:"Classifier"}),(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"browsing",className:"w-6 h-6 border-2 border-emerald-500 bg-emerald-700 hover:border-emerald-400 hover:bg-emerald-400 transition-colors",style:{left:-12,top:"70%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-emerald-200 font-medium pointer-events-none",style:{left:-80,top:"65%"},children:"Browsing"}),(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"tools",className:"w-6 h-6 border-2 border-green-500 bg-green-700 hover:border-green-400 hover:bg-green-400 transition-colors",style:{left:-12,top:"75%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-green-200 font-medium pointer-events-none",style:{left:-50,top:"70%"},children:"Tools"}),(0,r.jsx)(a.h7,{type:"target",position:o.yX.Left,id:"memory",className:"w-6 h-6 border-2 border-yellow-500 bg-yellow-700 hover:border-yellow-400 hover:bg-yellow-400 transition-colors",style:{left:-12,top:"90%"}}),(0,r.jsx)("div",{className:"absolute text-xs text-yellow-200 font-medium pointer-events-none",style:{left:-60,top:"85%"},children:"Memory"}),(0,r.jsx)(a.h7,{type:"source",position:o.yX.Right,id:"output",className:"w-6 h-6 border-2 border-[#ff6b35] bg-[#ff6b35] hover:border-orange-400 hover:bg-orange-400 transition-colors",style:{right:-12}}),(0,r.jsx)("div",{className:"absolute text-xs text-orange-200 font-medium pointer-events-none",style:{right:-60,top:"45%"},children:"Output"}),(0,r.jsxs)("div",{className:`min-w-[280px] rounded-lg border-2 transition-all duration-200 ${e.hasError?"border-red-500 bg-red-900/20":e.isConfigured?"border-gray-600 bg-gray-800/90":"border-yellow-500 bg-yellow-900/20"} backdrop-blur-sm shadow-lg hover:shadow-xl`,style:{borderColor:e.hasError?"#ef4444":e.isConfigured?"#ff6b35":"#eab308"},children:[(0,r.jsxs)("div",{className:"px-4 py-3 rounded-t-lg flex items-center gap-3",style:{background:e.hasError?"linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1))":"linear-gradient(135deg, #ff6b3520, #ff6b3510)"},children:[(0,r.jsx)("div",{className:"p-2 rounded-lg",style:{backgroundColor:e.hasError?"#ef444420":"#ff6b3520",color:e.hasError?"#ef4444":"#ff6b35"},children:(0,r.jsx)(i.A,{className:"w-4 h-4"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"font-medium text-white text-sm",children:"Central Router"}),(0,r.jsx)("div",{className:"text-xs text-gray-400 mt-1",children:"Smart routing hub for AI providers"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[p&&(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full",title:"Classifier Connected"}),d.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full",title:`${d.length} AI Providers`}),c.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full",title:`${c.length} Vision Models`}),x.length>0&&(0,r.jsx)("div",{className:"w-2 h-2 bg-cyan-500 rounded-full",title:`${x.length} Tools`})]})]}),(0,r.jsxs)("div",{className:"px-4 py-3 space-y-3",children:[d.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["AI Providers (",d.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:d.map(e=>(0,r.jsx)("span",{className:"text-xs bg-blue-900/30 text-blue-300 px-2 py-0.5 rounded-full border border-blue-700/30",children:e.name},e.id))})]}),c.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Vision Models (",c.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:c.map(e=>(0,r.jsx)("span",{className:"text-xs bg-purple-900/30 text-purple-300 px-2 py-0.5 rounded-full border border-purple-700/30",children:e.name},e.id))})]}),x.length>0&&(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Tools (",x.length,"):"]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:x.map(e=>(0,r.jsx)("span",{className:`text-xs px-2 py-0.5 rounded-full border ${"connected"===e.status?"bg-green-900/30 text-green-300 border-green-700/30":"bg-yellow-900/30 text-yellow-300 border-yellow-700/30"}`,children:e.name},e.id))})]}),n?.routingStrategy&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Strategy: ",n.routingStrategy.replace("_"," ").toUpperCase()]}),!p&&(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Connect classifier for smart routing"}),0===d.length&&0===c.length&&(0,r.jsx)("div",{className:"text-xs text-red-300 bg-red-900/20 px-2 py-1 rounded",children:"❌ No AI providers connected"}),x.length>0&&(0,r.jsxs)("div",{className:"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded",children:["\uD83D\uDD27 ",x.length," tool",x.length>1?"s":""," available"]}),(d.length>0||c.length>0)&&p&&(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Ready for smart routing"})]})]})]})},tool:function({data:e,onUpdate:s}){let t=e.config,l=t?.toolType,a=l?j.s0[l]:"\uD83D\uDD27",o=l?j.RW[l]:"External Tool",i=t?.connectionStatus||"disconnected",d=t?.isAuthenticated||!1;return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(n,{data:e,icon:v.A,color:"#06b6d4",hasInput:!1,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[a?(0,r.jsx)(y.default,{src:a,alt:o,width:20,height:20,className:"rounded-sm"}):(0,r.jsx)("span",{className:"text-lg",children:"\uD83D\uDD27"}),(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:o})]}),(0,r.jsx)("div",{className:"flex items-center gap-1",children:d&&"connected"===i?(0,r.jsx)(f.A,{className:"h-4 w-4 text-green-400"}):(0,r.jsx)(b.A,{className:"h-4 w-4 text-yellow-400"})})]}),(0,r.jsxs)("div",{className:`text-xs ${(()=>{if(d&&"connected"===i)return"text-green-400";switch(i){case"expired":case"revoked":case"error":return"text-red-400";default:return"text-yellow-400"}})()} flex items-center gap-1`,children:[(()=>{if(d&&"connected"===i)return"Connected";switch(i){case"expired":return"Expired";case"revoked":return"Revoked";case"error":return"Error";default:return"Not Connected"}})(),t?.providerUserEmail&&(0,r.jsxs)("span",{className:"text-gray-400",children:["• ",t.providerUserEmail]})]}),t?.timeout&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Timeout: ",t.timeout,"s"]}),(0,r.jsx)("div",{className:`text-xs px-2 py-1 rounded ${d&&"connected"===i?"text-green-300 bg-green-900/20":"text-yellow-300 bg-yellow-900/20"}`,children:d&&"connected"===i?"✓ Tool connected":"⚠️ Needs connection"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"External Tool Integration"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Connect to external services like Google Drive, Notion, Gmail, and more."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})})},memory:function({data:e,id:s}){let t=e.config,l=t?.memoryName,o=t?.maxSize||10240,i=t?.encryption!==!1,d=(0,a.Yu)().filter(e=>e.source===s).map(e=>e.target),[c,x]=(0,w.useState)(null),[p,g]=(0,w.useState)(!1);return(0,r.jsx)(n,{data:e,icon:N.A,color:"#ec4899",hasInput:!1,hasOutput:!0,children:(0,r.jsx)("div",{className:"space-y-3",children:l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-white",children:["\uD83E\uDDE0 ",l]}),(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Max: ",Math.round(o/1024),"MB | ",i?"\uD83D\uDD12 Encrypted":"\uD83D\uDD13 Plain"]}),p?(0,r.jsx)("div",{className:"text-xs text-gray-400 bg-gray-700/30 px-2 py-1 rounded",children:"\uD83D\uDD04 Loading memory stats..."}):c?(0,r.jsxs)("div",{className:"text-xs text-pink-300 bg-pink-900/20 px-2 py-1 rounded space-y-1",children:[(0,r.jsxs)("div",{children:["\uD83D\uDCCA ",c.entriesCount," entries"]}),(0,r.jsxs)("div",{children:["\uD83D\uDCBE ",c.totalSize," used"]}),"Never"!==c.lastUpdate&&(0,r.jsxs)("div",{children:["\uD83D\uDD52 Updated ",c.lastUpdate]})]}):(0,r.jsx)("div",{className:"text-xs text-gray-400 bg-gray-700/30 px-2 py-1 rounded",children:"\uD83D\uDCBE Empty - No data stored yet"}),d.length>0&&(0,r.jsxs)("div",{className:"text-xs text-blue-300 bg-blue-900/20 px-2 py-1 rounded",children:["\uD83D\uDD17 Connected to ",d.length," node",d.length>1?"s":""]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/20 px-2 py-1 rounded",children:"✅ Active & Ready"})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"\uD83E\uDDE0 Plug & Play Memory"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Intelligent memory brain for connected nodes. Automatically handles storage, retrieval, and persistence."}),(0,r.jsx)("div",{className:"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded",children:"⚠️ Needs configuration"})]})})})},planner:function({data:e}){let s=e.config,t=s?.providerId,l=s?.modelId,a=s?.maxSubtasks||10;return(0,r.jsx)(n,{data:e,icon:i.A,color:"#8b5cf6",hasInput:!1,hasOutput:!0,children:(0,r.jsxs)("div",{className:"space-y-3",children:[t&&l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:{openai:"OpenAI",anthropic:"Anthropic",google:"Google",groq:"Groq",deepseek:"DeepSeek",openrouter:"OpenRouter"}[t]||t}),(0,r.jsx)("span",{className:"text-xs text-gray-400",children:(e=>{if(!e)return"";let s=e.split("/");return s[s.length-1]})(l)})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max subtasks: ",a]}),s?.parameters?.temperature!==void 0&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Temperature: ",s.parameters.temperature]})]}):(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Configure AI model for planning"}),(0,r.jsx)("div",{className:"text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded",children:"\uD83D\uDCCB Planning Agent"})]})})},browsing:function({data:e,id:s}){(0,a.Yu)(),(0,a.pk)();let t=e.config,l=t?.providerId,o=t?.modelId,i=l?k[l]:"#10b981",d=l?I[l]:"Browsing AI",c=t?.maxSites||5,x=t?.timeout||30,p=t?.enableScreenshots??!0,g=t?.enableFormFilling??!0,u=t?.searchEngines||["google"],m=()=>{let e=[];return p&&e.push("\uD83D\uDCF8 Screenshots"),g&&e.push("\uD83D\uDCDD Forms"),t?.enableCaptchaSolving&&e.push("\uD83D\uDD10 CAPTCHAs"),e};return(0,r.jsx)(n,{data:e,icon:A.A,color:"string"==typeof i?i:"#10b981",hasInput:!1,hasOutput:!0,inputHandles:[{id:"plan",label:"Plan",position:"left"},{id:"memory",label:"Memory",position:"left"}],children:(0,r.jsxs)("div",{className:"space-y-3",children:[l?(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:d}),(0,r.jsx)("span",{className:"text-xs bg-gradient-to-r from-green-500 to-blue-500 text-white px-2 py-0.5 rounded-full",children:"Browsing"})]}),o&&(0,r.jsxs)("div",{className:"text-xs text-gray-300 bg-gray-700/50 px-2 py-1 rounded",children:["Model: ",o]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max sites: ",c," | Timeout: ",x,"s"]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Engines: ",u.join(", ")]}),m().length>0&&(0,r.jsx)("div",{className:"text-xs text-gray-400",children:m().join(" • ")}),t?.maxDepth&&(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["Max depth: ",t.maxDepth," levels"]})]}):(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:"Browsing Agent"}),(0,r.jsx)("div",{className:"text-xs text-yellow-400",children:"⚠️ Not Configured"})]}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Configure AI provider for browsing"})]}),(0,r.jsx)("div",{className:"text-xs text-green-300 bg-green-900/30 px-2 py-1 rounded",children:"\uD83C\uDF10 Autonomous Agent"}),(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"Requires: Planner + Memory inputs"})]})})}}}};