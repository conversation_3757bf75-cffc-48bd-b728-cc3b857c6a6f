{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "zNdIDrSvIuysDohOel6L-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "6112ac0e1b8dda47e2afa5c7c51991bd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ddc44ec25df9e02f3f403e5fa1c37f05d78b26c731310d672af42e12f47b82bc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "df1d8e6627477ba137233a458f5b624185f61ed1cfaca14bee6b809bad8ca488"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "zNdIDrSvIuysDohOel6L-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "6112ac0e1b8dda47e2afa5c7c51991bd", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ddc44ec25df9e02f3f403e5fa1c37f05d78b26c731310d672af42e12f47b82bc", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "df1d8e6627477ba137233a458f5b624185f61ed1cfaca14bee6b809bad8ca488"}}}, "sortedMiddleware": ["/"]}