(()=>{var e={};e.id=5283,e.ids=[5283],e.modules={2281:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>l});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l={children:["",{children:["test-browsing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74105)),"C:\\Ro<PERSON>ey App\\rokey-app\\src\\app\\test-browsing\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\RoKey App\\rokey-app\\src\\app\\test-browsing\\page.tsx"],d={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-browsing/page",pathname:"/test-browsing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},50223:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(43210);class n{static getApiEndpoint(){let e=process.env.NEXTAUTH_URL||"https://roukey.online";return`${e}/api/tools/web-browsing`}static async navigate(e,t){let s={action:"navigate",url:e,...t};return this.makeRequest(s)}static async search(e,t){let s={action:"search",query:e,searchEngine:t?.searchEngine||"google",...t};return this.makeRequest(s)}static async takeScreenshot(e,t){let s={action:"screenshot",url:e,...t};return this.makeRequest(s)}static async executeCustom(e,t,s,r){let a={action:"custom",url:e,code:t,context:s,...r};return this.makeRequest(a)}static async getStatus(){try{let e=await fetch(this.getApiEndpoint(),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){throw e}}static async makeRequest(e){try{let t=this.getApiEndpoint(),s=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),r=await s.json();if(!s.ok)throw Error(r.details||r.error||`HTTP error! status: ${s.status}`);return r}catch(e){throw e}}static async executeFromNodeConfig(e,t){let{extractionType:s,searchEngine:r,customSelector:a,timeout:n}=e,o={searchEngine:r||"google",extractionType:s||"content",customSelector:a,timeout:n||30};return"search"===s?this.search(t,o):"screenshot"===s?this.takeScreenshot(t,o):"custom"===s&&a?this.navigate(t,o):this.isUrl(t)?this.navigate(t,o):this.search(t,o)}static isUrl(e){try{return new URL(e),!0}catch{return e.startsWith("http://")||e.startsWith("https://")||e.includes(".")}}static getExampleUsage(e){switch(e){case"search":return'Example: "latest iPhone price" or "weather in New York"';case"screenshot":return'Example: "https://example.com" (will take a screenshot)';case"custom":return'Example: "https://example.com" (will extract using custom selector)';default:return'Example: "https://example.com" or "search query"'}}static getActionDescription(e){let{extractionType:t,searchEngine:s,customSelector:r}=e;switch(t){case"search":return`Search ${s||"Google"} and return top results`;case"screenshot":return"Take a screenshot of the webpage";case"custom":return`Extract content using selector: ${r||"custom selector"}`;default:return"Navigate to webpage and extract content"}}}function o(){let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!1),[i,c]=(0,a.useState)(null),[l,p]=(0,a.useState)(null),d=async()=>{o(!0),c(null);try{let e=await n.search("when is the next world cup",{searchEngine:"google",extractionType:"search"});t(e)}catch(e){c(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}},u=async()=>{o(!0),c(null);try{let e=await n.navigate("https://example.com",{extractionType:"content"});t(e)}catch(e){c(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}},h=async()=>{o(!0),c(null);try{let e=await n.takeScreenshot("https://example.com");t(e)}catch(e){c(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}},m=async()=>{o(!0),c(null);try{let e=await n.getStatus();p(e)}catch(e){c(e instanceof Error?e.message:"Unknown error")}finally{o(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-[#040716] text-white p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Web Browsing Tool Test"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Test Actions"}),(0,r.jsx)("button",{onClick:d,disabled:s,className:"w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors",children:s?"Loading...":"Test Search (World Cup)"}),(0,r.jsx)("button",{onClick:u,disabled:s,className:"w-full px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg transition-colors",children:s?"Loading...":"Test Navigate (example.com)"}),(0,r.jsx)("button",{onClick:h,disabled:s,className:"w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 rounded-lg transition-colors",children:s?"Loading...":"Test Screenshot (example.com)"}),(0,r.jsx)("button",{onClick:m,disabled:s,className:"w-full px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 rounded-lg transition-colors",children:s?"Loading...":"Check Service Status"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold",children:"Service Status"}),l&&(0,r.jsx)("div",{className:"bg-gray-800 p-4 rounded-lg",children:(0,r.jsx)("pre",{className:"text-sm overflow-auto",children:JSON.stringify(l,null,2)})})]})]}),i&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-2",children:"Error"}),(0,r.jsx)("p",{className:"text-red-300",children:i})]}),e&&(0,r.jsxs)("div",{className:"bg-gray-800 p-6 rounded-lg",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Result"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Action:"}),(0,r.jsx)("span",{className:"ml-2 text-green-400",children:e.action})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Timestamp:"}),(0,r.jsx)("span",{className:"ml-2",children:e.timestamp})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Data:"}),(0,r.jsx)("pre",{className:"mt-2 p-4 bg-gray-900 rounded text-sm overflow-auto max-h-96",children:JSON.stringify(e.data,null,2)})]})]})]})]})})}},53875:(e,t,s)=>{Promise.resolve().then(s.bind(s,74105))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74105:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\test-browsing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\test-browsing\\page.tsx","default")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},90827:(e,t,s)=>{Promise.resolve().then(s.bind(s,50223))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,5449,4912],()=>s(2281));module.exports=r})();