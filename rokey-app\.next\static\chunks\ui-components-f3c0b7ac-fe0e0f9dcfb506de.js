"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4842],{95494:(e,t,s)=>{s.d(t,{A:()=>m});var r=s(95155),a=s(6874),n=s.n(a),l=s(12115),i=s(27016),o=s(22261),c=s(35695),d=s(83298),u=s(52643),x=s(90441);function m(){var e,t,a,m,h,g,y;let{isCollapsed:b,isHovered:p,toggleSidebar:f}=(0,o.c)(),v=(0,c.usePathname)(),{user:j,subscriptionStatus:w}=(0,d.R)(),[N,k]=(0,l.useState)(!1),[C,A]=(0,l.useState)(!1),[P,S]=(0,l.useState)(!1),_=(0,u.createSupabaseBrowserClient)();(0,l.useEffect)(()=>{let e=()=>{S(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let E=(null==j||null==(e=j.user_metadata)?void 0:e.first_name)||(null==j||null==(a=j.user_metadata)||null==(t=a.full_name)?void 0:t.split(" ")[0])||"User",K=E.charAt(0).toUpperCase()+((null==j||null==(g=j.user_metadata)||null==(h=g.last_name)||null==(m=h.charAt(0))?void 0:m.toUpperCase())||(null==(y=E.charAt(1))?void 0:y.toUpperCase())||"U"),z=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/tools":return{title:"Tool Connections",subtitle:"Manage connected tools"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(v),D=(null==w?void 0:w.tier)==="free"?"Free Plan":(null==w?void 0:w.tier)==="starter"?"Starter Plan":(null==w?void 0:w.tier)==="professional"?"Professional Plan":"Free Plan",F=async()=>{try{let{clearAllUserCache:e}=await s.e(5738).then(s.bind(s,63171));await e(),await _.auth.signOut(),window.location.href="/auth/signin"}catch(e){try{localStorage.clear(),sessionStorage.clear()}catch(e){}window.location.href="/auth/signin"}};return(0,l.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),A(!0))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),(0,r.jsxs)("nav",{className:"header border-b border-gray-800/50 bg-[#040716] backdrop-blur-sm w-full",children:[(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(P&&(!b||p)?"max-w-7xl mx-auto":P?"max-w-none":"max-w-7xl mx-auto"),children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("button",{onClick:f,className:"lg:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",title:"Toggle sidebar",children:(0,r.jsx)(i.tK,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"RouKey"})}),(0,r.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)("span",{children:z.title}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-white font-medium",children:z.subtitle})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,r.jsxs)("div",{className:"hidden xl:block relative",children:[(0,r.jsxs)("button",{onClick:()=>A(!0),className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-gray-800/50 border border-gray-700 rounded-lg text-gray-400 hover:text-gray-200 hover:border-gray-600 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Search..."}),(0,r.jsx)("kbd",{className:"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-500 bg-gray-700 border border-gray-600 rounded",children:"⌘K"})]}),(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(i.$p,{className:"h-4 w-4 text-gray-500"})})]}),(0,r.jsx)("button",{onClick:()=>A(!0),className:"xl:hidden p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200",children:(0,r.jsx)(i.$p,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 relative",children:[(0,r.jsx)(i.XF,{className:"h-5 w-5 text-gray-400"}),(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,r.jsxs)("div",{className:"hidden sm:block relative",children:[(0,r.jsxs)("button",{onClick:()=>k(!N),className:"p-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 flex items-center space-x-1",children:[(0,r.jsx)(i.Vy,{className:"h-5 w-5 text-gray-400"}),(0,r.jsx)(i.D3,{className:"h-3 w-3 text-gray-400 transition-transform duration-200 ".concat(N?"rotate-180":"")})]}),N&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>k(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-gray-700 py-1 z-20",children:[(0,r.jsxs)(n(),{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,r.jsx)(i.Vy,{className:"h-4 w-4 mr-3 text-gray-400"}),"Account Settings"]}),(0,r.jsxs)(n(),{href:"/billing",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,r.jsx)(i.BF,{className:"h-4 w-4 mr-3 text-gray-400"}),"Billing & Plans"]}),(0,r.jsxs)(n(),{href:"/docs",className:"flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 transition-colors duration-200",onClick:()=>k(!1),children:[(0,r.jsx)(i.AQ,{className:"h-4 w-4 mr-3 text-gray-400"}),"Documentation"]}),(0,r.jsx)("hr",{className:"my-1 border-gray-700"}),(0,r.jsxs)("button",{onClick:F,className:"flex items-center w-full px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 transition-colors duration-200",children:[(0,r.jsx)(i.Rz,{className:"h-4 w-4 mr-3 text-red-400"}),"Sign Out"]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-800/50 transition-colors duration-200 cursor-pointer",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-semibold text-sm",children:K})}),(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:E}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:D})]})]})]})]})}),(0,r.jsx)(x.K,{isOpen:C,onClose:()=>A(!1)})]})}}}]);