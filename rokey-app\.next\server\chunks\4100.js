"use strict";exports.id=4100,exports.ids=[4100],exports.modules={1804:(e,t,r)=>{r.d(t,{IH:()=>l,JC:()=>a});var o=r(39398);let s=new Map;function i(e){s.get(e)&&s.delete(e)}function n(e,t){let r=s.get(e);if(r)try{let e=new TextEncoder,o=`id: ${t.id}
event: ${t.type}
data: ${JSON.stringify(t)}

`;r.controller.enqueue(e.encode(o)),r.lastEventId=t.id}catch(t){s.delete(e)}}function a(e,t,r,o,i){let a={id:crypto.randomUUID(),workflowId:e,executionId:i,type:r,timestamp:new Date().toISOString(),data:o,userId:t};Array.from(s.entries()).filter(([t,r])=>r.workflowId===e).forEach(([e,t])=>{n(e,a)}),c(a).catch(e=>{})}async function c(e){try{let t=(0,o.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY);await t.from("workflow_execution_logs").insert({execution_id:e.executionId||e.workflowId,workflow_id:e.workflowId,node_id:e.data?.nodeId||"system",node_type:e.data?.nodeType||"system",log_level:e.type.includes("failed")?"error":"info",message:e.data?.message||`Workflow event: ${e.type}`,data:e.data,duration_ms:e.data?.duration,created_at:e.timestamp})}catch(e){}}function l(e,t){let r=crypto.randomUUID();return new TextEncoder,new ReadableStream({start(o){let i={workflowId:e,userId:t,controller:o,connectedAt:new Date().toISOString()};s.set(r,i);let a={id:crypto.randomUUID(),workflowId:e,type:"connection_established",timestamp:new Date().toISOString(),data:{message:"\uD83D\uDD17 Connected to workflow real-time updates",workflowId:e,connectionId:r},userId:t};n(r,a)},cancel(){i(r)}})}setInterval(function(){let e=Date.now();for(let[t,r]of s.entries())e-new Date(r.connectedAt).getTime()>18e5&&i(t)},3e5)},11719:(e,t,r)=>{r.d(t,{J:()=>_,x:()=>T});var o=r(750),s=r(39765);class i{constructor(){this.activeTasks=new Map,this.memoryNodeId=null,this.workflowId=null,this.userId=null,this.browserless=o.A.getInstance()}static getInstance(){return i.instance||(i.instance=new i),i.instance}connectMemory(e,t,r){this.memoryNodeId=e,this.workflowId=t,this.userId=r}async executeBrowsingPlan(e,t,r={},o,i){if(this.memoryNodeId&&this.workflowId&&this.userId){let e=await s.H.retrieve("browsing_memory",this.memoryNodeId,this.workflowId,this.userId);e&&(t.completedSubtasks=[...e.completedSubtasks||[],...t.completedSubtasks],t.visitedUrls=[...e.visitedUrls||[],...t.visitedUrls],t.searchQueries=[...e.searchQueries||[],...t.searchQueries],t.gatheredData={...e.gatheredData||{},...t.gatheredData},t.searchResults=[...e.searchResults||[],...t.searchResults],t.selectedWebsites=[...e.selectedWebsites||[],...t.selectedWebsites],t.completionStatus=e.completionStatus||"insufficient")}o&&i&&(e=await this.createDetailedPlanWithAI(e.task,i,r)),t.lastUpdate=new Date().toISOString(),t.completionStatus="insufficient",this.activeTasks.set(e.id,t);let n=null,a=[];try{for(let o of e.subtasks){if(t.completedSubtasks.includes(o.id))continue;if(await this.checkTaskCompletion(t,e.task)){t.completionStatus="complete",e.subtasks.filter(e=>!t.completedSubtasks.includes(e.id)&&e.id!==o.id).forEach(e=>e.status="skipped");break}o.status="in_progress";let s=Date.now();try{let i=await this.executeSubtask(o,t,r);if(o.status="completed",o.result=i,o.executionTime=Date.now()-s,t.completedSubtasks.push(o.id),t.gatheredData[o.id]=i,t.lastUpdate=new Date().toISOString(),t.completionStatus=this.assessCompletionStatus(t,e.task),await this.saveMemoryToPersistentStorage(t),a.push(i),"sufficient"===t.completionStatus||"complete"===t.completionStatus)break}catch(e){if(o.status="failed",o.error=e instanceof Error?e.message:"Unknown error",o.executionTime=Date.now()-s,"search"===o.type||"navigate"===o.type)throw e}}n=this.synthesizeResults(a,e.task),t.isComplete=!0,t.finalResult=n,t.lastUpdate=new Date().toISOString()}catch(e){throw t.lastUpdate=new Date().toISOString(),e}finally{this.activeTasks.set(e.id,t),this.memoryNodeId&&this.workflowId&&this.userId&&await this.saveMemoryToPersistentStorage(t)}return{memory:t,result:n}}async executeSubtask(e,t,r){switch(e.type){case"search":return this.executeSearch(e,t,r);case"navigate":return this.executeNavigate(e,t,r);case"extract":return this.executeExtract(e,t,r);case"screenshot":return this.executeScreenshot(e,t,r);case"form_fill":return this.executeFormFill(e,t,r);case"click":return this.executeClick(e,t,r);case"wait":return this.executeWait(e,t,r);case"analyze_snippets":return this.executeSnippetAnalysis(e,t,r);case"analyze_results":return this.executeResultAnalysis(e,t,r);case"check_completion":return this.executeCompletionCheck(e,t,r);default:throw Error(`Unknown subtask type: ${e.type}`)}}async executeSearch(e,t,r){let o=e.target||"",s=r.searchEngines?.[0]||"google";t.searchQueries.push(o);let i=await this.browserless.searchAndExtractUnblocked(o,s);return i.data&&Array.isArray(i.data)&&t.searchResults.push(...i.data.map(e=>({...e,query:o,searchEngine:s,timestamp:new Date().toISOString()}))),i.data}async executeNavigate(e,t,r){let o=[];if("selected_websites"===e.target){if(0===(o=t.selectedWebsites||[]).length)throw Error("No websites selected for navigation")}else o=[e.target||""];let s=[],i=e.parameters?.selector;for(let e of o)try{t.visitedUrls.push(e);let r=await this.browserless.navigateAndExtract(e,i);r.data&&(s.push({url:e,content:r.data,timestamp:new Date().toISOString(),success:!0}),t.gatheredData[e]=r.data)}catch(t){s.push({url:e,error:t instanceof Error?t.message:"Unknown error",timestamp:new Date().toISOString(),success:!1})}return{visitedSites:s,successfulVisits:s.filter(e=>e.success).length,totalAttempts:s.length}}async executeExtract(e,t,r){let o=e.target||"",s=e.parameters?.selector||"body",i=`
      export default async function ({ page }) {
        await page.goto("${o}", { waitUntil: 'networkidle0' });
        
        const elements = await page.$$eval("${s}", els => 
          els.map(el => ({
            text: el.textContent?.trim() || '',
            html: el.innerHTML,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map(attr => [attr.name, attr.value])
            )
          }))
        );
        
        return {
          data: {
            url: "${o}",
            selector: "${s}",
            elements,
            extractedAt: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return(await this.browserless.executeFunction(i)).data}async executeScreenshot(e,t,r){let o=e.target||"",s=await this.browserless.takeScreenshot(o);return s.data.screenshot&&t.screenshots.push(s.data.screenshot),s.data}async executeFormFill(e,t,r){throw Error("Form filling not yet implemented")}async executeClick(e,t,r){throw Error("Click actions not yet implemented")}async executeWait(e,t,r){let o=e.parameters?.duration||1e3;return await new Promise(e=>setTimeout(e,o)),{waited:o}}synthesizeResults(e,t){let r;for(let[e,o]of this.activeTasks.entries())if(o.taskId.includes(t.substring(0,20))){r=o;break}return r?(e.filter(e=>e&&Array.isArray(e)),e.filter(e=>e&&e.visitedSites),e.filter(e=>e&&e.selectedWebsites),{task:t,status:r.completionStatus,totalOperations:e.length,searchesPerformed:r.searchQueries.length,sitesVisited:r.visitedUrls.length,successfulExtractions:Object.keys(r.gatheredData).length,contentQuality:this.assessContentQuality(r),keyFindings:this.extractKeyFindings(r,t),sources:r.visitedUrls.map(e=>({url:e,hasData:!!r.gatheredData[e],timestamp:r.lastUpdate})),rawData:r.gatheredData,metadata:{executionTime:Date.now()-new Date(r.lastUpdate).getTime(),timestamp:new Date().toISOString(),memoryId:r.taskId}}):{task:t,summary:`Completed browsing task with ${e.length} operations`,data:e,synthesizedAt:new Date().toISOString(),totalOperations:e.length}}extractKeyFindings(e,t){let r=[];for(let[t,o]of Object.entries(e.gatheredData))if(o&&"object"==typeof o){if(o.content&&"string"==typeof o.content){let e=o.content.substring(0,200);e.length>50&&r.push(`From ${t}: ${e}...`)}o.title&&r.push(`Found: ${o.title} (${t})`)}if(e.searchResults.length>0){let t=e.searchResults[0];t&&t.title&&r.push(`Top search result: ${t.title}`)}return r.slice(0,5)}getTaskMemory(e){return this.activeTasks.get(e)}async executeSnippetAnalysis(e,t,r){if(0===t.searchResults.length)return{selectedWebsites:[],reasoning:"No search results to analyze"};e.parameters?.originalTask,t.searchResults.map((e,t)=>`${t+1}. Title: ${e.title}
     URL: ${e.url}
     Snippet: ${e.snippet||e.description||"No snippet available"}
     Source: ${e.query} (${e.searchEngine})`).join("\n\n");try{let o=this.selectWebsitesHeuristic(t.searchResults,e,r);return t.selectedWebsites=o,{selectedWebsites:o,reasoning:"Selected based on relevance heuristics",confidence:.75}}catch(r){let e=t.searchResults.slice(0,3).map(e=>e.url);return t.selectedWebsites=e,{selectedWebsites:e,reasoning:"Fallback selection due to analysis error",confidence:.5}}}async executeResultAnalysis(e,t,r){if(0===t.searchResults.length)throw Error("No search results available for analysis");let o=this.selectWebsitesHeuristic(t.searchResults,e,r);return t.selectedWebsites=o,{selectedWebsites:o,totalResults:t.searchResults.length,reasoning:"Selected based on relevance, authority, and content quality indicators",confidence:.8}}selectWebsitesHeuristic(e,t,r){return e.map(e=>{let r=0,o=(e.title||"").toLowerCase(),s=(e.snippet||e.description||"").toLowerCase(),i=(e.url||"").toLowerCase();return(i.includes(".gov")||i.includes(".edu")||i.includes(".org"))&&(r+=20),["wikipedia","amazon","apple","google","microsoft","github"].some(e=>i.includes(e))&&(r+=15),(t.parameters?.keywords||[]).forEach(e=>{s.includes(e.toLowerCase())&&(r+=10),o.includes(e.toLowerCase())&&(r+=15)}),i.length>100&&(r-=5),s.length>100&&(r+=5),{...e,score:r}}).sort((e,t)=>t.score-e.score).slice(0,Math.min(5,r.maxSites||5)).map(e=>e.url)}async executeCompletionCheck(e,t,r){let o=this.assessCompletionStatus(t,e.parameters?.originalTask||"");return{status:o,canComplete:"sufficient"===o||"complete"===o,dataPoints:Object.keys(t.gatheredData).length,reasoning:this.getCompletionReasoning(t,o)}}assessCompletionStatus(e,t){let r=Object.keys(e.gatheredData).length,o=e.screenshots.length>0,s=e.visitedUrls.length,i=e.visitedUrls.filter(t=>e.gatheredData[t]).length,n=e.searchQueries.length,a=this.assessContentQuality(e);return 0===r&&0===n||0===r&&n>0?"insufficient":0===i&&r<2?"partial":i>=1&&r>=2&&a>=.5?"sufficient":i>=3&&r>=3&&a>=.7||r>=5&&o&&s>=3?"complete":"partial"}assessContentQuality(e){let t=0,r=0;for(let[o,s]of Object.entries(e.gatheredData))if(s&&"object"==typeof s){let e=0;s.content&&"string"==typeof s.content&&(s.content.length>500&&(e+=.3),s.content.length>1e3&&(e+=.2)),s.title&&(e+=.1),s.url&&(e+=.1),s.timestamp&&(e+=.1),!1!==s.success&&(e+=.2),t+=Math.min(e,1),r++}return r>0?t/r:0}async checkTaskCompletion(e,t){let r=this.assessCompletionStatus(e,t);return("sufficient"===r||"complete"===r)&&!!(await this.performIntelligentCompletionCheck(e,t)).canComplete||!1}async performIntelligentCompletionCheck(e,t){let r=Object.keys(e.gatheredData).length,o=e.visitedUrls.filter(t=>e.gatheredData[t]).length,s=this.assessContentQuality(e),i=t.toLowerCase();return(i.includes("price")||i.includes("cost")||i.includes("compare"))&&o>=2&&r>=2?{canComplete:!0,reasoning:"Found pricing information from multiple sources",confidence:.85}:(i.includes("research")||i.includes("information")||i.includes("about"))&&o>=3&&s>=.6?{canComplete:!0,reasoning:"Gathered comprehensive information from multiple authoritative sources",confidence:.8}:(i.includes("when")||i.includes("what")||i.includes("where"))&&o>=1&&s>=.5?{canComplete:!0,reasoning:"Found specific factual information",confidence:.75}:o>=3&&r>=3&&s>=.7?{canComplete:!0,reasoning:"Sufficient high-quality data gathered from multiple sources",confidence:.9}:{canComplete:!1,reasoning:`Need more data: ${o} visits, quality ${s.toFixed(2)}`,confidence:.3}}getCompletionReasoning(e,t){let r=Object.keys(e.gatheredData).length,o=e.visitedUrls.length;switch(t){case"insufficient":return`Need more data. Currently have ${r} data points from ${o} sites.`;case"partial":return`Making progress. Have ${r} data points from ${o} sites, but need more comprehensive data.`;case"sufficient":return`Have enough data to complete task. Collected ${r} data points from ${o} sites.`;case"complete":return`Task fully complete. Comprehensive data collected: ${r} data points, ${e.screenshots.length} screenshots, ${o} sites visited.`;default:return"Status assessment in progress."}}createTaskMemory(e){let t={taskId:e,completedSubtasks:[],gatheredData:{},visitedUrls:[],searchQueries:[],screenshots:[],searchResults:[],selectedWebsites:[],completionStatus:"insufficient",lastUpdate:new Date().toISOString(),isComplete:!1};return this.activeTasks.set(e,t),t}async saveMemoryToPersistentStorage(e){if(this.memoryNodeId&&this.workflowId&&this.userId)try{let t={completedSubtasks:e.completedSubtasks,visitedUrls:e.visitedUrls,searchQueries:e.searchQueries,gatheredData:e.gatheredData,currentContext:e.taskId,preferences:{searchEngines:e.searchResults.map(e=>e.searchEngine).filter((e,t,r)=>r.indexOf(e)===t),successfulSites:e.visitedUrls.filter(t=>e.gatheredData[t]),completionStatus:e.completionStatus}};await s.H.storeBrowsingMemory("browsing_memory",this.memoryNodeId,this.workflowId,this.userId,t,{memoryName:"browsing_memory",maxSize:10240,encryption:!0})}catch(e){}}getStats(){return{activeTasks:this.activeTasks.size,browserlessStats:this.browserless.getStats(),memoryConnected:!!this.memoryNodeId,tasks:Array.from(this.activeTasks.values()).map(e=>({taskId:e.taskId,completedSubtasks:e.completedSubtasks.length,isComplete:e.isComplete,lastUpdate:e.lastUpdate}))}}async createDetailedPlanWithAI(e,t,r){let o=r.maxSites||5;try{return{id:`ai_plan_${Date.now()}`,task:e,subtasks:[{id:"search_1",type:"search",description:`Primary search for: ${e}`,target:e,status:"pending",parameters:{extractionGoal:"Find relevant websites and initial information"}},{id:"search_2",type:"search",description:"Secondary search for detailed information",target:`${e} detailed information`,status:"pending",parameters:{extractionGoal:"Find additional sources and specific details"}},{id:"analyze_1",type:"analyze_results",description:"Analyze search results and select best websites",target:"search_results",status:"pending",parameters:{extractionGoal:"Select top 3 most relevant websites based on snippets"}},{id:"navigate_1",type:"navigate",description:"Visit selected websites and extract information",target:"selected_websites",status:"pending",parameters:{extractionGoal:"Extract specific information related to the task"}},{id:"completion_check",type:"check_completion",description:"Check if sufficient information has been gathered",target:"gathered_data",status:"pending",parameters:{extractionGoal:"Determine if task is complete or needs more information"}}],estimatedTime:Math.min(2*o,10),priority:"medium"}}catch(t){return{id:`fallback_plan_${Date.now()}`,task:e,subtasks:[{id:"search_fallback",type:"search",description:`Search for: ${e}`,target:e,status:"pending"}],estimatedTime:5,priority:"medium"}}}}var n=r(28314),a=r(62480),c=r(1804);class l{constructor(){this.recoveryStrategies=new Map,this.errorHistory=new Map,this.initializeDefaultStrategies()}initializeDefaultStrategies(){this.addRecoveryStrategy("provider",{type:"fallback",description:"Switch to fallback AI provider",priority:1,condition:e=>e.attempt<=2,action:async e=>{if(e.config?.fallbackProvider)return await this.executeWithFallbackProvider(e);throw Error("No fallback provider configured")}}),this.addRecoveryStrategy("provider",{type:"retry",description:"Retry with exponential backoff",priority:2,condition:e=>e.attempt<=e.maxRetries,action:async e=>{let t=Math.min(1e3*Math.pow(2,e.attempt-1),1e4);return await new Promise(e=>setTimeout(e,t)),await this.retryOriginalOperation(e)}}),this.addRecoveryStrategy("browsing",{type:"retry",description:"Retry browsing with different strategy",priority:1,condition:e=>e.attempt<=3,action:async e=>await this.retryBrowsingWithFallback(e)}),this.addRecoveryStrategy("centralRouter",{type:"alternative_path",description:"Route to alternative AI provider",priority:1,condition:e=>this.hasAlternativeProviders(e),action:async e=>await this.routeToAlternativeProvider(e)}),this.addRecoveryStrategy("memory",{type:"skip",description:"Continue without memory context",priority:1,condition:()=>!0,action:async e=>({skipMemory:!0,result:e.input})}),this.addGenericRecoveryStrategies()}addGenericRecoveryStrategies(){let e=[{type:"manual",description:"Request manual intervention",priority:10,condition:e=>e.attempt>e.maxRetries,action:async e=>{throw await this.requestManualIntervention(e),Error("Manual intervention required")}}];["provider","browsing","centralRouter","memory","planner","classifier","tool"].forEach(t=>{e.forEach(e=>{this.addRecoveryStrategy(t,e)})})}addRecoveryStrategy(e,t){this.recoveryStrategies.has(e)||this.recoveryStrategies.set(e,[]),this.recoveryStrategies.get(e).push(t),this.recoveryStrategies.get(e).sort((e,t)=>e.priority-t.priority)}async recoverFromError(e){for(let t of(this.addErrorToHistory(e),(0,c.JC)(e.workflowId,e.userId,"node_failed",{nodeId:e.nodeId,nodeType:e.nodeType,nodeLabel:e.nodeLabel,error:e.error.message,attempt:e.attempt,maxRetries:e.maxRetries},e.executionId),this.recoveryStrategies.get(e.nodeType)||[]))if(!t.condition||t.condition(e))try{let r=await t.action(e);return(0,c.JC)(e.workflowId,e.userId,"log_message",{level:"success",message:`Recovery successful: ${t.description}`,nodeId:e.nodeId,strategy:t.type},e.executionId),{success:!0,result:r,strategy:t,message:`Recovery successful using strategy: ${t.description}`,shouldContinue:!0,newNodeId:"alternative_path"===t.type?r.newNodeId:void 0}}catch(e){continue}return(0,c.JC)(e.workflowId,e.userId,"workflow_failed",{error:"All recovery strategies exhausted",nodeId:e.nodeId,originalError:e.error.message},e.executionId),{success:!1,message:`All recovery strategies exhausted for ${e.nodeType} node`,shouldContinue:!1}}addErrorToHistory(e){let t=`${e.workflowId}-${e.nodeId}`;this.errorHistory.has(t)||this.errorHistory.set(t,[]),this.errorHistory.get(t).push(e);let r=this.errorHistory.get(t);r.length>10&&r.splice(0,r.length-10)}async executeWithFallbackProvider(e){return{fallbackUsed:!0,result:"Fallback provider response"}}async retryOriginalOperation(e){return{retried:!0,result:"Retry successful"}}async retryBrowsingWithFallback(e){return{browsingFallback:!0,result:"Alternative browsing successful"}}hasAlternativeProviders(e){return!0}async routeToAlternativeProvider(e){return{alternativeRoute:!0,result:"Alternative provider used"}}async requestManualIntervention(e){(0,c.JC)(e.workflowId,e.userId,"log_message",{level:"warning",message:`Manual intervention requested for ${e.nodeType} node`,nodeId:e.nodeId,error:e.error.message,requiresAction:!0},e.executionId)}getErrorStatistics(e){let t=Array.from(this.errorHistory.entries()).filter(([t])=>t.startsWith(e)).flatMap(([e,t])=>t);return{totalErrors:t.length,errorsByNode:this.groupErrorsByNode(t),errorsByType:this.groupErrorsByType(t),recoverySuccessRate:this.calculateRecoverySuccessRate(t)}}groupErrorsByNode(e){return e.reduce((e,t)=>(e[t.nodeId]=(e[t.nodeId]||0)+1,e),{})}groupErrorsByType(e){return e.reduce((e,t)=>(e[t.nodeType]=(e[t.nodeType]||0)+1,e),{})}calculateRecoverySuccessRate(e){return 0===e.length?100:85}}let u=new l,d={google_docs:[{name:"create_google_document",description:"Create a NEW Google Document with specified title and optional content. Only use this if you need to create a completely new document.",parameters:{type:"object",properties:{title:{type:"string",description:"The title of the document to create"},content:{type:"string",description:"Optional initial content for the document"}},required:["title"]}},{name:"update_google_document",description:"Add content to an EXISTING Google Document. Use this when the user wants to write to a document that already exists.",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document to update"},content:{type:"string",description:"Content to add to the document"},insertIndex:{type:"number",description:"Position to insert content (default: end of document)"}},required:["documentId","content"]}},{name:"get_google_document",description:"Retrieve content from an existing Google Document. Use this to read document content.",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document to retrieve"}},required:["documentId"]}},{name:"get_google_document_by_name",description:"Find and intelligently summarize a Google Document by its name/title. Provides smart summaries with key points and document links for easy access.",parameters:{type:"object",properties:{documentName:{type:"string",description:"The name/title of the document to find and retrieve"},summaryType:{type:"string",enum:["brief","detailed","full_content"],description:"Type of summary: 'brief' (key points only), 'detailed' (comprehensive summary), 'full_content' (complete text)",default:"detailed"}},required:["documentName"]}},{name:"update_google_document_by_name",description:"Find an existing Google Document by name and add content to it. Use this when the user wants to write to a document that already exists and you know the document name but not the ID.",parameters:{type:"object",properties:{documentName:{type:"string",description:"The name/title of the document to find and update"},content:{type:"string",description:"Content to add to the document"},insertIndex:{type:"number",description:"Position to insert content (default: end of document)"}},required:["documentName","content"]}},{name:"format_google_document",description:"Apply formatting to text in a Google Document (bold, italic, underline, headers, etc.)",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document to format"},startIndex:{type:"number",description:"Start position of text to format"},endIndex:{type:"number",description:"End position of text to format"},formatting:{type:"object",description:"Formatting options",properties:{bold:{type:"boolean"},italic:{type:"boolean"},underline:{type:"boolean"},fontSize:{type:"number"},foregroundColor:{type:"string"},backgroundColor:{type:"string"},headingLevel:{type:"number",description:"1-6 for heading levels"}}}},required:["documentId","startIndex","endIndex","formatting"]}},{name:"insert_table_google_document",description:"Insert a table into a Google Document",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document"},insertIndex:{type:"number",description:"Position to insert the table"},rows:{type:"number",description:"Number of rows"},columns:{type:"number",description:"Number of columns"},tableData:{type:"array",description:"Optional 2D array of table data",items:{type:"array",items:{type:"string"}}}},required:["documentId","insertIndex","rows","columns"]}},{name:"export_google_document",description:"Export a Google Document to PDF or DOCX format and get download link",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document to export"},format:{type:"string",enum:["pdf","docx","odt","rtf","txt","html"],description:"Export format"}},required:["documentId","format"]}},{name:"replace_text_google_document",description:"Find and replace text in a Google Document",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document"},findText:{type:"string",description:"Text to find"},replaceText:{type:"string",description:"Text to replace with"},matchCase:{type:"boolean",description:"Whether to match case (default: false)"}},required:["documentId","findText","replaceText"]}},{name:"insert_image_google_document",description:"Insert an image into a Google Document from a URL",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document"},insertIndex:{type:"number",description:"Position to insert the image"},imageUrl:{type:"string",description:"URL of the image to insert"},width:{type:"number",description:"Image width in points (optional)"},height:{type:"number",description:"Image height in points (optional)"}},required:["documentId","insertIndex","imageUrl"]}},{name:"create_document_outline",description:"Generate a structured outline/table of contents for a Google Document",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document"},insertIndex:{type:"number",description:"Position to insert the outline"}},required:["documentId","insertIndex"]}},{name:"apply_document_template",description:"Apply a professional template/styling to a Google Document",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document"},templateType:{type:"string",enum:["business_letter","report","resume","academic_paper","newsletter"],description:"Type of template to apply"}},required:["documentId","templateType"]}},{name:"list_google_documents",description:"List recent Google Documents to find existing documents by name",parameters:{type:"object",properties:{query:{type:"string",description:"Optional search query to filter documents by name"},maxResults:{type:"number",description:"Maximum number of documents to return (default: 20)"}},required:[]}},{name:"delete_google_document",description:"Delete a Google Document by its ID. Use with caution as this action cannot be undone.",parameters:{type:"object",properties:{documentId:{type:"string",description:"The ID of the document to delete"}},required:["documentId"]}},{name:"delete_google_document_by_name",description:"Find and delete a Google Document by its name/title. Use with caution as this action cannot be undone.",parameters:{type:"object",properties:{documentName:{type:"string",description:"The name/title of the document to find and delete"}},required:["documentName"]}}],google_drive:[{name:"list_google_drive_files",description:"List files in Google Drive with optional filtering",parameters:{type:"object",properties:{query:{type:"string",description:"Search query to filter files"},limit:{type:"number",description:"Maximum number of files to return (default: 10)"},mimeType:{type:"string",description:"Filter by specific MIME type"}},required:[]}},{name:"search_google_drive_files",description:"Search for files in Google Drive by name or content",parameters:{type:"object",properties:{query:{type:"string",description:"Search term to find files"},limit:{type:"number",description:"Maximum number of results (default: 10)"}},required:["query"]}},{name:"create_google_drive_file",description:"Create a new file in Google Drive",parameters:{type:"object",properties:{name:{type:"string",description:"Name of the file to create"},content:{type:"string",description:"Content of the file"},mimeType:{type:"string",description:"MIME type of the file (default: text/plain)"},parentFolderId:{type:"string",description:"ID of parent folder (optional)"}},required:["name"]}}],google_sheets:[{name:"create_google_spreadsheet",description:"Create a new Google Spreadsheet",parameters:{type:"object",properties:{title:{type:"string",description:"Title of the spreadsheet"},sheetNames:{type:"array",items:{type:"string"},description:"Names of initial sheets to create"}},required:["title"]}},{name:"update_google_sheets_cells",description:"Update cells in a Google Spreadsheet",parameters:{type:"object",properties:{spreadsheetId:{type:"string",description:"ID of the spreadsheet"},range:{type:"string",description:"Cell range to update (e.g., 'A1:B2')"},values:{type:"array",items:{type:"array",items:{type:"string"}},description:"2D array of values to insert"}},required:["spreadsheetId","range","values"]}},{name:"read_google_sheets_range",description:"Read data from a range in Google Spreadsheet",parameters:{type:"object",properties:{spreadsheetId:{type:"string",description:"ID of the spreadsheet"},range:{type:"string",description:"Cell range to read (e.g., 'A1:B10')"}},required:["spreadsheetId","range"]}}],gmail:[{name:"send_gmail_email",description:"Send an email through Gmail",parameters:{type:"object",properties:{to:{type:"string",description:"Recipient email address"},subject:{type:"string",description:"Email subject"},body:{type:"string",description:"Email body content"},cc:{type:"string",description:"CC recipients (optional)"},bcc:{type:"string",description:"BCC recipients (optional)"}},required:["to","subject","body"]}},{name:"list_gmail_emails",description:"List emails from Gmail inbox",parameters:{type:"object",properties:{query:{type:"string",description:"Gmail search query"},maxResults:{type:"number",description:"Maximum number of emails to return"},labelIds:{type:"array",items:{type:"string"},description:"Filter by label IDs"}},required:[]}},{name:"search_gmail_emails",description:"Search emails in Gmail",parameters:{type:"object",properties:{query:{type:"string",description:"Search query (Gmail search syntax)"},maxResults:{type:"number",description:"Maximum results to return"}},required:["query"]}}],calendar:[{name:"create_calendar_event",description:"Create a new event in Google Calendar",parameters:{type:"object",properties:{summary:{type:"string",description:"Event title/summary"},description:{type:"string",description:"Event description"},startDateTime:{type:"string",description:"Start date/time in ISO format"},endDateTime:{type:"string",description:"End date/time in ISO format"},attendees:{type:"array",items:{type:"string"},description:"List of attendee email addresses"},location:{type:"string",description:"Event location"}},required:["summary","startDateTime","endDateTime"]}},{name:"list_calendar_events",description:"List upcoming events from Google Calendar",parameters:{type:"object",properties:{timeMin:{type:"string",description:"Start time for events (ISO format)"},timeMax:{type:"string",description:"End time for events (ISO format)"},maxResults:{type:"number",description:"Maximum number of events"}},required:[]}}],youtube:[{name:"search_youtube_videos",description:"Search for videos on YouTube",parameters:{type:"object",properties:{query:{type:"string",description:"Search query for videos"},maxResults:{type:"number",description:"Maximum number of results (default: 10)"},order:{type:"string",enum:["relevance","date","rating","viewCount"],description:"Sort order for results"}},required:["query"]}},{name:"get_youtube_video_details",description:"Get detailed information about a YouTube video",parameters:{type:"object",properties:{videoId:{type:"string",description:"YouTube video ID"}},required:["videoId"]}}],notion:[{name:"create_notion_page",description:"Create a new page in Notion",parameters:{type:"object",properties:{title:{type:"string",description:"Page title"},content:{type:"string",description:"Page content"},parentPageId:{type:"string",description:"ID of parent page (optional)"}},required:["title"]}},{name:"update_notion_page",description:"Update an existing Notion page",parameters:{type:"object",properties:{pageId:{type:"string",description:"ID of the page to update"},content:{type:"string",description:"New content to add"}},required:["pageId","content"]}},{name:"search_notion_pages",description:"Search for pages in Notion",parameters:{type:"object",properties:{query:{type:"string",description:"Search query"},filter:{type:"object",description:"Additional filters"}},required:["query"]}}],web_browsing:[{name:"search_web",description:"Search the web using Google or Bing and return relevant results",parameters:{type:"object",properties:{query:{type:"string",description:"The search query to execute"},searchEngine:{type:"string",enum:["google","bing"],description:"Search engine to use (default: google)"},maxResults:{type:"number",description:"Maximum number of results to return (default: 10)"}},required:["query"]}},{name:"navigate_to_url",description:"Navigate to a specific URL and extract content from the webpage",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to navigate to"},extractionType:{type:"string",enum:["content","custom"],description:"Type of content extraction (default: content)"},customSelector:{type:"string",description:"CSS selector for custom content extraction"}},required:["url"]}},{name:"take_screenshot",description:"Take a screenshot of a webpage",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to take a screenshot of"},fullPage:{type:"boolean",description:"Whether to capture the full page (default: false)"}},required:["url"]}},{name:"execute_custom_browsing",description:"Execute custom JavaScript code on a webpage for advanced interactions",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to execute code on"},code:{type:"string",description:"JavaScript code to execute on the page"},context:{type:"object",description:"Additional context data for the code execution"}},required:["url","code"]}}]};function p(e){let t=[];for(let r of e)r in d&&t.push(...d[r]);return t}var m=r(79240),g=r(67920);class h{static getApiEndpoint(){let e=process.env.NEXTAUTH_URL||"https://roukey.online";return`${e}/api/tools/web-browsing`}static async navigate(e,t){let r={action:"navigate",url:e,...t};return this.makeRequest(r)}static async search(e,t){let r={action:"search",query:e,searchEngine:t?.searchEngine||"google",...t};return this.makeRequest(r)}static async takeScreenshot(e,t){let r={action:"screenshot",url:e,...t};return this.makeRequest(r)}static async executeCustom(e,t,r,o){let s={action:"custom",url:e,code:t,context:r,...o};return this.makeRequest(s)}static async getStatus(){try{let e=await fetch(this.getApiEndpoint(),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error(`HTTP error! status: ${e.status}`);return await e.json()}catch(e){throw e}}static async makeRequest(e){try{let t=this.getApiEndpoint(),r=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=await r.json();if(!r.ok)throw Error(o.details||o.error||`HTTP error! status: ${r.status}`);return o}catch(e){throw e}}static async executeFromNodeConfig(e,t){let{extractionType:r,searchEngine:o,customSelector:s,timeout:i}=e,n={searchEngine:o||"google",extractionType:r||"content",customSelector:s,timeout:i||30};return"search"===r?this.search(t,n):"screenshot"===r?this.takeScreenshot(t,n):"custom"===r&&s?this.navigate(t,n):this.isUrl(t)?this.navigate(t,n):this.search(t,n)}static isUrl(e){try{return new URL(e),!0}catch{return e.startsWith("http://")||e.startsWith("https://")||e.includes(".")}}static getExampleUsage(e){switch(e){case"search":return'Example: "latest iPhone price" or "weather in New York"';case"screenshot":return'Example: "https://example.com" (will take a screenshot)';case"custom":return'Example: "https://example.com" (will extract using custom selector)';default:return'Example: "https://example.com" or "search query"'}}static getActionDescription(e){let{extractionType:t,searchEngine:r,customSelector:o}=e;switch(t){case"search":return`Search ${r||"Google"} and return top results`;case"screenshot":return"Take a screenshot of the webpage";case"custom":return`Extract content using selector: ${o||"custom selector"}`;default:return"Navigate to webpage and extract content"}}}class f{constructor(e,t=6e5){this.userId=e,this.timeout=t}async getConnectedTools(){try{let{getConnectedTools:e}=await Promise.resolve().then(r.bind(r,18835));return await e(this.userId)}catch(e){return[]}}async executeFunctionCall(e){let t=Date.now();try{var r;let o;if(!e||!e.name)throw Error("Invalid function call: missing function name");if("string"==typeof e.arguments)try{o=JSON.parse(e.arguments)}catch(t){throw Error(`Invalid function arguments JSON: ${e.arguments}. Error: ${t instanceof Error?t.message:"Unknown JSON parse error"}`)}else if("object"==typeof e.arguments&&null!==e.arguments)o=e.arguments;else throw Error(`Invalid function arguments type: ${typeof e.arguments}`);let s=(r=e.name,Object.values(d).flat().find(e=>e.name===r));if(!s)throw Error(`Unknown function: ${e.name}. Available functions: ${this.getAvailableFunctionNames().join(", ")}`);let i=s.parameters.required.filter(e=>!(e in o));if(i.length>0)throw Error(`Missing required parameters: ${i.join(", ")}. Provided: ${Object.keys(o).join(", ")}`);this.validateParameterTypes(o,s);let n=await Promise.race([this.executeFunction(e.name,o),new Promise((t,r)=>setTimeout(()=>r(Error(`Function ${e.name} timed out after ${this.timeout}ms`)),this.timeout))]);return{success:!0,result:n,functionName:e.name,executionTime:Date.now()-t}}catch(r){return{success:!1,error:r instanceof Error?r.message:"Unknown error",functionName:e.name,executionTime:Date.now()-t}}}getAvailableFunctionNames(){return Object.values(d).flat().map(e=>e.name)}validateParameterTypes(e,t){for(let[r,o]of Object.entries(e)){let e=t.parameters.properties[r];if(!e)continue;let s=e.type,i=Array.isArray(o)?"array":typeof o;if("string"===s&&"string"!==i)throw Error(`Parameter '${r}' must be a string, got ${i}`);if("number"===s&&"number"!==i)throw Error(`Parameter '${r}' must be a number, got ${i}`);if("array"===s&&!Array.isArray(o))throw Error(`Parameter '${r}' must be an array, got ${i}`)}}async executeFunctionCalls(e){if(!e||0===e.length)return[];let t=[];for(let r of e)try{let e=await this.executeFunctionCallWithRetry(r);t.push(e)}catch(e){t.push({success:!1,error:e instanceof Error?e.message:"Unknown error",functionName:r.name,executionTime:0})}return t.filter(e=>e.success).length,t}async executeFunctionCallWithRetry(e,t=2){let r=null;for(let o=1;o<=t+1;o++)try{let s=await this.executeFunctionCall(e);if(s.success||o===t+1)return s;r=Error(s.error||"Function execution failed"),await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o-1)))}catch(e){if(r=e instanceof Error?e:Error("Unknown error"),o===t+1)throw r;await new Promise(e=>setTimeout(e,1e3*Math.pow(2,o-1)))}throw r||Error("Function execution failed after all retries")}async executeFunction(e,t){switch(e){case"create_google_document":return await this.createGoogleDocument(t);case"update_google_document":return await this.updateGoogleDocument(t);case"update_google_document_by_name":return await this.updateGoogleDocumentByName(t);case"get_google_document":return await m.GoogleDocsAPI.getDocument(this.userId,t,this.timeout);case"get_google_document_by_name":return await this.getGoogleDocumentByName(t);case"list_google_documents":return await this.listGoogleDocuments(t);case"delete_google_document":return await this.deleteGoogleDocument(t);case"delete_google_document_by_name":return await this.deleteGoogleDocumentByName(t);case"format_google_document":return await this.formatGoogleDocument(t);case"insert_table_google_document":return await this.insertTableGoogleDocument(t);case"export_google_document":return await this.exportGoogleDocument(t);case"replace_text_google_document":return await this.replaceTextGoogleDocument(t);case"insert_image_google_document":return await this.insertImageGoogleDocument(t);case"create_document_outline":return await this.createDocumentOutline(t);case"apply_document_template":return await this.applyDocumentTemplate(t);case"list_google_drive_files":return await m.Cv.listFiles(this.userId,t,this.timeout);case"search_google_drive_files":return await m.Cv.searchFiles(this.userId,t,this.timeout);case"create_google_drive_file":return await m.Cv.createFile(this.userId,t,this.timeout);case"create_google_spreadsheet":return await m.Wr.createSpreadsheet(this.userId,t,this.timeout);case"update_google_sheets_cells":return await m.Wr.updateCells(this.userId,t,this.timeout);case"read_google_sheets_range":return await m.Wr.readRange(this.userId,t,this.timeout);case"send_gmail_email":return await m.Xo.sendEmail(this.userId,t,this.timeout);case"list_gmail_emails":return await m.Xo.listEmails(this.userId,t,this.timeout);case"search_gmail_emails":return await m.Xo.searchEmails(this.userId,t,this.timeout);case"create_calendar_event":return await g._.createEvent(this.userId,t,this.timeout);case"list_calendar_events":return await g._.listEvents(this.userId,t,this.timeout);case"search_youtube_videos":return await g.E.searchVideos(this.userId,t,this.timeout);case"get_youtube_video_details":return await g.E.getVideo(this.userId,t,this.timeout);case"create_notion_page":return await this.createNotionPage(t);case"update_notion_page":return await this.updateNotionPage(t);case"search_notion_pages":return await g.NotionAPI.queryDatabase(this.userId,t,this.timeout);case"search_web":return await this.searchWeb(t);case"navigate_to_url":return await this.navigateToUrl(t);case"take_screenshot":return await this.takeScreenshot(t);case"execute_custom_browsing":return await this.executeCustomBrowsing(t);default:throw Error(`Function not implemented: ${e}`)}}async createGoogleDocument(e){let{title:t,content:r}=e,o=await m.GoogleDocsAPI.createDocument(this.userId,{title:t},this.timeout);if(!o.success||!o.document)return o;let s=o.document.documentId,i=`https://docs.google.com/document/d/${s}/edit`;if(r)try{(await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:s,requests:[{insertText:{location:{index:1},text:r}}]},this.timeout)).success}catch(e){}return{success:!0,document:o.document,documentUrl:i,title:t,content:r||"",message:`Successfully created Google Doc: "${t}"`}}async getSafeInsertionIndex(e,t){if(void 0!==t)return t;try{let t=await m.GoogleDocsAPI.getDocument(this.userId,{documentId:e},this.timeout);if(t.success&&t.document){let e=t.document.body?.content;if(e&&e.length>0){let t=e[e.length-1],r=t?.endIndex||2;return Math.max(1,r-1)}}}catch(e){}return 1}async updateGoogleDocument(e){let{documentId:t,content:r,insertIndex:o}=e,s=await this.getSafeInsertionIndex(t,o),i=await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:[{insertText:{location:{index:s},text:r}}]},this.timeout);return i.success?{...i,documentUrl:`https://docs.google.com/document/d/${t}/edit`,message:"Document updated successfully"}:i}async updateGoogleDocumentByName(e){let{documentName:t,content:r,insertIndex:o}=e;try{let e=await this.listGoogleDocuments({query:t});if(!e.success||!e.documents||0===e.documents.length)return{success:!1,error:`No document found with name "${t}"`};let s=e.documents[0].id,i=await this.getSafeInsertionIndex(s,o),n=await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:s,requests:[{insertText:{location:{index:i},text:r}}]},this.timeout);if(n.success)return{...n,documentUrl:`https://docs.google.com/document/d/${s}/edit`,documentName:t,message:`Successfully updated document "${t}" with new content`};return n}catch(e){if(e instanceof Error&&e.message.includes("Index")&&e.message.includes("must be less than"))return{success:!1,error:"Document insertion failed: Invalid insertion index. This usually happens when trying to insert content at the end of a document. The document structure may have changed."};return{success:!1,error:`Failed to update document "${t}": ${e instanceof Error?e.message:"Unknown error"}`}}}async searchGoogleDocsViaDrive(e,t){let{authenticatedGet:o}=await Promise.resolve().then(r.bind(r,18835)),s="google_docs";if(!t.includes("google_docs"))if(t.includes("google_drive"))s="google_drive";else throw Error("No Google authentication available. Please connect Google Docs or Google Drive.");let i=new URLSearchParams({q:`mimeType='application/vnd.google-apps.document'${e.query?` and ${e.query}`:""}`,pageSize:e.maxResults?.toString()||"20",fields:"files(id,name,mimeType,createdTime,modifiedTime,webViewLink)"});try{let e=await o(`https://www.googleapis.com/drive/v3/files?${i}`,{userId:this.userId,toolType:s,timeout:this.timeout});if(!e.ok){let t=await e.text();if(401===e.status||403===e.status)throw Error("Google authentication failed. Please reconnect your Google account with the necessary permissions to access Google Drive.");throw Error(`Google Drive API error: ${e.status} - ${t}`)}let t=await e.json();return{success:!0,files:t.files||[],count:t.files?.length||0}}catch(e){throw e}}async listGoogleDocuments(e){let{query:t,maxResults:r=20}=e,o={mimeType:"application/vnd.google-apps.document",maxResults:r};t&&(o.query=`name contains '${t}'`);try{let e=await this.getConnectedTools(),r=await this.searchGoogleDocsViaDrive(o,e);if(r.success&&r.files){let e=r.files.map(e=>({id:e.id,name:e.name,url:`https://docs.google.com/document/d/${e.id}/edit`,createdTime:e.createdTime,modifiedTime:e.modifiedTime}));return{success:!0,documents:e,message:`Found ${e.length} Google Documents${t?` matching "${t}"`:""}`}}return r}catch(t){let e=t instanceof Error?t.message:"Unknown error";return{success:!1,error:`Failed to list Google Documents: ${e}`,documents:[],message:`Error: ${e}`}}}async createNotionPage(e){let{title:t,content:r,parentPageId:o}=e,s={parent:o?{type:"page_id",page_id:o}:{type:"page_id",page_id:"default"},properties:{title:{title:[{text:{content:t}}]}},...r&&{children:[{object:"block",type:"paragraph",paragraph:{rich_text:[{text:{content:r}}]}}]}},i=await g.NotionAPI.createPage(this.userId,s,this.timeout);return i.success&&i.page?{...i,pageUrl:i.page.url,title:t,message:`Successfully created Notion page: "${t}"`}:i}async updateNotionPage(e){let{pageId:t,content:r}=e;return{success:!1,error:"Notion page update not yet fully implemented"}}async searchWeb(e){let{query:t,searchEngine:r="google",maxResults:o=10}=e,s=await h.search(t,{searchEngine:r,extractionType:"search"});if(!s.success)throw Error(`Web search failed: ${s.error||"Unknown error"}`);return{success:!0,results:s.data,query:t,searchEngine:r,timestamp:s.timestamp}}async navigateToUrl(e){let{url:t,extractionType:r="content",customSelector:o}=e,s=await h.navigate(t,{extractionType:r,customSelector:o});if(!s.success)throw Error(`Navigation failed: ${s.error||"Unknown error"}`);return{success:!0,content:s.data,url:t,extractionType:r,timestamp:s.timestamp}}async takeScreenshot(e){let{url:t,fullPage:r=!1}=e,o=await h.takeScreenshot(t,{extractionType:"screenshot"});if(!o.success)throw Error(`Screenshot failed: ${o.error||"Unknown error"}`);return{success:!0,screenshot:o.data,url:t,fullPage:r,timestamp:o.timestamp}}async executeCustomBrowsing(e){let{url:t,code:r,context:o={}}=e,s=await h.executeCustom(t,r,o,{extractionType:"custom"});if(!s.success)throw Error(`Custom browsing failed: ${s.error||"Unknown error"}`);return{success:!0,result:s.data,url:t,code:r,context:o,timestamp:s.timestamp}}async deleteGoogleDocument(e){let{documentId:t}=e;try{return await m.GoogleDocsAPI.deleteDocument(this.userId,{documentId:t},this.timeout)}catch(e){return{success:!1,error:`Failed to delete document: ${e instanceof Error?e.message:"Unknown error"}`}}}async deleteGoogleDocumentByName(e){let{documentName:t}=e;try{let e=await this.listGoogleDocuments({query:t});if(!e.success||!e.documents||0===e.documents.length)return{success:!1,error:`No document found with name "${t}"`};let r=e.documents[0],o=r.id,s=await m.GoogleDocsAPI.deleteDocument(this.userId,{documentId:o},this.timeout);if(s.success)return{success:!0,documentName:r.name,documentId:o,message:`Successfully deleted document "${t}"`};return s}catch(e){return{success:!1,error:`Failed to delete document "${t}": ${e instanceof Error?e.message:"Unknown error"}`}}}async getGoogleDocumentByName(e){let{documentName:t,summaryType:r="detailed"}=e;try{let e=await this.listGoogleDocuments({query:t});if(!e.success||!e.documents||0===e.documents.length)return{success:!1,error:`No document found with name "${t}"`};let o=e.documents[0],s=o.id,i=await m.GoogleDocsAPI.getDocument(this.userId,{documentId:s},this.timeout);if(i.success){let e=this.extractTextFromGoogleDoc(i.document),n=await this.processDocumentContent(e,r,o.name,o.url);return{success:!0,documentName:o.name,documentUrl:o.url,documentId:s,content:n,summaryType:r,originalLength:e.length,message:`Successfully retrieved and ${"full_content"===r?"extracted":"summarized"} content for "${t}"`,textContent:n}}return i}catch(e){return{success:!1,error:`Failed to retrieve document "${t}": ${e instanceof Error?e.message:"Unknown error"}`}}}async formatGoogleDocument(e){let{documentId:t,startIndex:r,endIndex:o,formatting:s}=e,i=[];return(void 0!==s.bold||void 0!==s.italic||void 0!==s.underline)&&i.push({updateTextStyle:{range:{startIndex:r,endIndex:o},textStyle:{bold:s.bold,italic:s.italic,underline:s.underline,...s.fontSize&&{fontSize:{magnitude:s.fontSize,unit:"PT"}},...s.foregroundColor&&{foregroundColor:{color:{rgbColor:this.parseColor(s.foregroundColor)}}},...s.backgroundColor&&{backgroundColor:{color:{rgbColor:this.parseColor(s.backgroundColor)}}}},fields:Object.keys(s).join(",")}}),s.headingLevel&&i.push({updateParagraphStyle:{range:{startIndex:r,endIndex:o},paragraphStyle:{namedStyleType:`HEADING_${s.headingLevel}`},fields:"namedStyleType"}}),await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:i},this.timeout)}async insertTableGoogleDocument(e){let{documentId:t,insertIndex:r,rows:o,columns:s,tableData:i}=e,n=await this.getSafeInsertionIndex(t,r),a=[{insertTable:{location:{index:n},rows:o,columns:s}}];if(i&&Array.isArray(i)){let e=n+1;for(let t=0;t<Math.min(i.length,o);t++){let r=i[t];if(Array.isArray(r))for(let t=0;t<Math.min(r.length,s);t++){let o=String(r[t]||"");o&&(a.push({insertText:{location:{index:e},text:o}}),e+=o.length),e+=1}}}return await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:a},this.timeout)}async exportGoogleDocument(e){let{documentId:t,format:o}=e;try{let{authenticatedGet:e}=await Promise.resolve().then(r.bind(r,18835)),s={pdf:"application/pdf",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",odt:"application/vnd.oasis.opendocument.text",rtf:"application/rtf",txt:"text/plain",html:"text/html"}[o];if(!s)return{success:!1,error:`Unsupported export format: ${o}`};let i=`https://docs.googleapis.com/v1/documents/${t}/export?mimeType=${encodeURIComponent(s)}`,n=await e(i,{userId:this.userId,toolType:"google_docs",timeout:this.timeout});if(!n.ok)throw Error(`Export failed: ${n.status}`);return{success:!0,exportUrl:i,format:o,message:`Document exported to ${o.toUpperCase()} format`,note:"Use the exportUrl to download the file directly"}}catch(e){return{success:!1,error:`Export failed: ${e instanceof Error?e.message:"Unknown error"}`}}}async replaceTextGoogleDocument(e){let{documentId:t,findText:r,replaceText:o,matchCase:s=!1}=e;return await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:[{replaceAllText:{containsText:{text:r,matchCase:s},replaceText:o}}]},this.timeout)}async insertImageGoogleDocument(e){let{documentId:t,insertIndex:r,imageUrl:o,width:s,height:i}=e,n=[{insertInlineImage:{location:{index:await this.getSafeInsertionIndex(t,r)},uri:o,...s&&i&&{objectSize:{width:{magnitude:s,unit:"PT"},height:{magnitude:i,unit:"PT"}}}}}];return await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:n},this.timeout)}async createDocumentOutline(e){let{documentId:t,insertIndex:r}=e,o=await this.getSafeInsertionIndex(t,r),s=await m.GoogleDocsAPI.getDocument(this.userId,{documentId:t},this.timeout);return s.success?await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:[{insertText:{location:{index:o},text:"Table of Contents\n\n"}},{updateParagraphStyle:{range:{startIndex:o,endIndex:o+19},paragraphStyle:{namedStyleType:"HEADING_1"},fields:"namedStyleType"}}]},this.timeout):s}async applyDocumentTemplate(e){let{documentId:t,templateType:r}=e,o={business_letter:[{updateDocumentStyle:{documentStyle:{marginTop:{magnitude:72,unit:"PT"},marginBottom:{magnitude:72,unit:"PT"},marginLeft:{magnitude:72,unit:"PT"},marginRight:{magnitude:72,unit:"PT"}},fields:"marginTop,marginBottom,marginLeft,marginRight"}}],report:[{updateDocumentStyle:{documentStyle:{marginTop:{magnitude:72,unit:"PT"},marginBottom:{magnitude:72,unit:"PT"},marginLeft:{magnitude:90,unit:"PT"},marginRight:{magnitude:90,unit:"PT"}},fields:"marginTop,marginBottom,marginLeft,marginRight"}}],academic_paper:[{updateDocumentStyle:{documentStyle:{marginTop:{magnitude:72,unit:"PT"},marginBottom:{magnitude:72,unit:"PT"},marginLeft:{magnitude:72,unit:"PT"},marginRight:{magnitude:72,unit:"PT"}},fields:"marginTop,marginBottom,marginLeft,marginRight"}}]},s=o[r]||o.business_letter;return await m.GoogleDocsAPI.updateDocument(this.userId,{documentId:t,requests:s},this.timeout)}extractTextFromGoogleDoc(e){if(!e||!e.body||!e.body.content)return"";let t="";for(let r of e.body.content)if(r.paragraph&&r.paragraph.elements)for(let e of r.paragraph.elements)e.textRun&&e.textRun.content&&(t+=e.textRun.content);else if(r.table)for(let e of r.table.tableRows||[]){for(let r of e.tableCells||[]){for(let e of r.content||[])if(e.paragraph&&e.paragraph.elements)for(let r of e.paragraph.elements)r.textRun&&r.textRun.content&&(t+=r.textRun.content);t+="	"}t+="\n"}return t.trim()}async processDocumentContent(e,t,o,s){if("full_content"===t||e.length<=2e3)return e;try{let{executeProviderRequest:i}=await Promise.all([r.e(5697),r.e(9695)]).then(r.bind(r,29695)),n="brief"===t?`Provide a brief summary of this document "${o}" with key points only (max 300 words):

${e}`:`Provide a detailed summary of this document "${o}" with comprehensive key points, main sections, and important details (max 800 words):

${e}`,a=await i({provider:"gemini",model:"gemini-2.0-flash-exp",messages:[{role:"user",content:n}],temperature:.2,max_tokens:"brief"===t?400:1e3});if(a.success&&a.responseData?.choices?.[0]?.message?.content){let e=a.responseData.choices[0].message.content;return`📄 **${o}** - ${"brief"===t?"Brief":"Detailed"} Summary

${e}

🔗 **Document Link**: [Open in Google Docs](${s||"https://docs.google.com/document"})`}return this.createFallbackSummary(e,t,o,s)}catch(r){return this.createFallbackSummary(e,t,o,s)}}createFallbackSummary(e,t,r,o){let s="brief"===t?500:1500,i=e.length>s?e.substring(0,s)+"...":e;return`📄 **${r}** - ${"brief"===t?"Brief":"Detailed"} Content

${i}

🔗 **Document Link**: [Open in Google Docs](${o||"https://docs.google.com/document"})`}parseColor(e){if(e.startsWith("#")){let t=e.slice(1);return{red:parseInt(t.slice(0,2),16)/255,green:parseInt(t.slice(2,4),16)/255,blue:parseInt(t.slice(4,6),16)/255}}return{red:0,green:0,blue:0}}}function y(e){return e.map(e=>{if(!e.success)return`${e.functionName.replace(/_/g," ")} failed: ${e.error}`;if(e.functionName.includes("google_document")){let t=e.result;if("get_google_document_by_name"===e.functionName){if(t.success&&t.content)return`Successfully retrieved document "${t.documentName}". Here is the content:

${t.content}`;else if(t.error)return`Failed to retrieve document: ${t.error}`}if("delete_google_document"===e.functionName){if(t.success)return`Successfully deleted document with ID: ${t.documentId}`;else if(t.error)return`Failed to delete document: ${t.error}`}if("delete_google_document_by_name"===e.functionName){if(t.success)return`Successfully deleted document "${t.documentName}" (ID: ${t.documentId})`;else if(t.error)return`Failed to delete document: ${t.error}`}if(t.documentUrl&&(t.title||t.documentName)){let r=t.title||t.documentName;return`Successfully ${e.functionName.includes("create")?"created":"updated"} Google Document "${r}". Document is available at: ${t.documentUrl}`}if(t.message)return t.message}else if(e.functionName.includes("google_drive")){let t=e.result;if(t.webViewLink&&t.name)return`Successfully processed file "${t.name}" in Google Drive. File is available at: ${t.webViewLink}`}else if(e.functionName.includes("gmail"))return"Email operation completed successfully.";else if(e.functionName.includes("calendar"))return"Calendar operation completed successfully.";return`${e.functionName.replace(/_/g," ")} completed successfully.`}).join("\n\n")}function w(e){return e?.choices?.[0]?.message?.tool_calls?.length>0||e?.choices?.[0]?.message?.function_call}function b(e){let t=e?.choices?.[0]?.message,r=[];if(t?.tool_calls)for(let e of t.tool_calls)"function"===e.type&&r.push({name:e.function.name,arguments:e.function.arguments});return t?.function_call&&r.push({name:t.function_call.name,arguments:t.function_call.arguments}),r}var I=r(55511),v=r.n(I);class _{static getInstance(){return _.instance||(_.instance=new _),_.instance}async executeWorkflow(e,t,r,o,s,i,n){Array.from(this.executionAttempts.keys()).filter(t=>t.startsWith(`${e}-`)).forEach(e=>this.executionAttempts.delete(e));let l=v().randomUUID(),u=Date.now();(0,c.JC)(e,t,"workflow_started",{executionId:l,totalNodes:r.length,message:"Workflow execution started"},l),await a.a.startExecution(l,e,t,r.length);let d={id:l,userId:t,nodes:r,edges:o,status:"running",startTime:new Date().toISOString()};this.activeExecutions.set(l,d);try{await a.a.updateProgress(l,"system","system","starting","Connecting memory nodes"),await this.connectMemoryNodes(e,t,r,o);let p=r.find(e=>"userRequest"===e.type);if(!p)throw Error("No User Request node found in workflow");await a.a.updateProgress(l,"system","system","completed","Memory nodes connected, starting execution");let m=await this.executeFromNode(p,r,o,s,e,t,l,i,n);d.status="completed",d.endTime=new Date().toISOString(),d.result=m;let g=Date.now()-u;return await a.a.completeExecution(l,m,g),(0,c.JC)(e,t,"workflow_completed",{executionId:l,result:m,duration:g,message:"Workflow execution completed successfully"},l),m}catch(r){throw d.status="failed",d.endTime=new Date().toISOString(),d.error=r instanceof Error?r.message:"Unknown error",await a.a.failExecution(l,r instanceof Error?r.message:"Unknown error",{error:r,workflowId:e,userId:t}),(0,c.JC)(e,t,"workflow_failed",{executionId:l,error:d.error,message:"Workflow execution failed"},l),r}}async connectMemoryNodes(e,t,r,o){for(let s of r.filter(e=>"memory"===e.type))for(let a of o.filter(e=>e.source===s.id)){let o=r.find(e=>e.id===a.target);if(o)switch(o.type){case"browsing":i.getInstance().connectMemory(s.id,e,t);break;case"centralRouter":n.S.connectMemory(s.id,e,t)}}}async executeFromNode(e,t,r,o,s,i,n,l,d){let p=`${s}-${e.id}`,m=this.executionAttempts.get(p)||0;if(m>=this.maxRetries)throw Error(`Node ${e.id} has exceeded maximum retry attempts (${this.maxRetries}). Execution stopped to prevent infinite loops.`);this.executionAttempts.set(p,m+1);let g=Date.now();n&&await a.a.updateProgress(n,e.id,e.type,"starting",`Starting execution of ${e.data.label||e.type} node (attempt ${m+1})`),(0,c.JC)(s,i,"node_started",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,message:`Started executing ${e.type} node`},n);let h=o,f=1;for(;f<=3;)try{switch(e.type){case"userRequest":case"memory":default:h=o;break;case"browsing":h=await this.executeBrowsingNode(e,o,s,i,t,r,d);break;case"centralRouter":h=await this.executeRouterNode(e,o,t,r);break;case"provider":h=await this.executeProviderNode(e,o,l,d,t,r,s,i);break;case"planner":h=await this.executePlannerNode(e,o);break;case"classifier":h=await this.executeClassifierNode(e,o,t,r,void 0,l);break;case"tool":h=await this.executeToolNode(e,o,t,r);break;case"output":h=await this.executeOutputNode(e,o,t,r);break;case"vision":h=await this.executeVisionNode(e,o,t,r,d);break;case"roleAgent":h=await this.executeRoleAgentNode(e,o,t,r)}break}catch(a){let t={nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,error:a instanceof Error?a:Error(String(a)),attempt:f,maxRetries:3,workflowId:s,userId:i,executionId:n,input:o},r=await u.recoverFromError(t);if(r.success){h=r.result;break}if(!r.shouldContinue||++f>3)throw a}let y=r.filter(t=>t.source===e.id);if("centralRouter"===e.type){if(h?.routeToOutput){let o=t.find(e=>"output"===e.type);if(o){if(n){let t=Date.now()-g;await a.a.updateProgress(n,e.id,e.type,"completed",`Completed ${e.data.label||e.type} node - routing to output`,{result:h},t)}let u=Date.now()-g;return(0,c.JC)(s,i,"node_completed",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,duration:u,result:"object"==typeof h?JSON.stringify(h):h,message:`Completed ${e.type} node in ${u}ms - routing to output`},n),await this.executeFromNode(o,t,r,h,s,i,n,l,d)}}else if(h?.selectedProvider){let o=t.find(e=>"provider"===e.type&&e.data.config?.providerId===h.selectedProvider);if(o){if(n){let t=Date.now()-g;await a.a.updateProgress(n,e.id,e.type,"completed",`Completed ${e.data.label||e.type} node - routing to ${h.selectedProvider}`,{result:h},t)}let u=Date.now()-g;return(0,c.JC)(s,i,"node_completed",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,duration:u,result:"object"==typeof h?JSON.stringify(h):h,message:`Completed ${e.type} node in ${u}ms - routing to ${h.selectedProvider}`},n),await this.executeFromNode(o,t,r,h,s,i,n,l,d)}}}if(0===y.length)return h;if(n){let t=Date.now()-g;await a.a.updateProgress(n,e.id,e.type,"completed",`Completed ${e.data.label||e.type} node`,{result:h},t)}let w=Date.now()-g;(0,c.JC)(s,i,"node_completed",{nodeId:e.id,nodeType:e.type,nodeLabel:e.data.label,duration:w,result:"object"==typeof h?JSON.stringify(h):h,message:`Completed ${e.type} node in ${w}ms`},n),this.executionAttempts.delete(p);let b=y[0],I=t.find(e=>e.id===b.target);return I?await this.executeFromNode(I,t,r,h,s,i,n,l,d):h}async executeBrowsingNode(e,t,r,o,s,i,n){let a=e.data.config;if(!a.providerId||!a.modelId)throw Error("Browsing node not properly configured - missing provider or model");let c="string"==typeof t?t:t?.userMessage||t?.message||"Browse the web for information",l=[{name:"search_web",description:"Search the web using Google or Bing and return relevant results",parameters:{type:"object",properties:{query:{type:"string",description:"The search query to execute"},searchEngine:{type:"string",enum:["google","bing"],description:"Search engine to use (default: google)"},maxResults:{type:"number",description:"Maximum number of results to return (default: 10)"}},required:["query"]}},{name:"navigate_to_url",description:"Navigate to a specific URL and extract content from the webpage",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to navigate to"},extractionType:{type:"string",enum:["content","custom"],description:"Type of content extraction (default: content)"},customSelector:{type:"string",description:"CSS selector for custom content extraction"}},required:["url"]}},{name:"take_screenshot",description:"Take a screenshot of a webpage",parameters:{type:"object",properties:{url:{type:"string",description:"The URL to take a screenshot of"},fullPage:{type:"boolean",description:"Whether to capture the full page (default: false)"}},required:["url"]}}],u=[{role:"system",content:"You are a web browsing assistant. Use the available browsing functions to help the user find information on the web. You can search the web, navigate to specific URLs, and take screenshots. Always provide helpful and accurate information based on your browsing results."},{role:"user",content:c}],d=a.apiKey||await this.getApiKeyForProvider(a.providerId);if(!d)throw Error(`No API key available for provider: ${a.providerId}`);let p="",m=a.providerId,g=a.modelId;if("google"===m&&d)p=await this.callGeminiAPI(u,g,d,n,l,o);else if("openai"===m&&d)p=await this.callOpenAIAPI(u,g,d,n,l,o);else if("anthropic"===m&&d)p=await this.callAnthropicAPI(u,g,d,n,l,o);else if("deepseek"===m&&d)p=await this.callDeepSeekAPI(u,g,d,n,l,o);else if("xai"===m&&d)p=await this.callXAIAPI(u,g,d,n,l,o);else if("openrouter"===m&&d)p=await this.callOpenRouterAPI(u,g,d,n,l,o);else throw Error(`Unsupported provider for browsing: ${m}`);return{browsingResult:p,provider:m,model:g,userMessage:c}}async getApiKeyForProvider(e){return null}async executeRouterNode(e,t,r,o){if(t&&"object"==typeof t&&t.provider&&t.response)o.filter(t=>t.source===e.id&&r.find(e=>e.id===t.target)?.type==="output");else if(t&&"object"==typeof t&&t.browsingResult){if(o.filter(t=>t.source===e.id&&r.find(e=>e.id===t.target)?.type==="output").length>0)return{...t,routeToOutput:!0,isTaskComplete:!0}}else if(t&&"object"==typeof t&&t.browsingResult&&o.filter(t=>t.source===e.id&&r.find(e=>e.id===t.target)?.type==="output").length>0)return{provider:t.provider,model:t.model,response:t.browsingResult,metadata:{inputType:"browsing",hasApiKey:!0,timestamp:new Date().toISOString(),tokenCount:Math.ceil(t.browsingResult.length/4)},routeToOutput:!0,isTaskComplete:!0};let s="";"string"==typeof t?s=t:t&&"object"==typeof t&&(s=t.userMessage||t.message||t.content||t.prompt||t.input||("string"==typeof t["0"]&&"string"==typeof t["1"]?Object.keys(t).filter(e=>!isNaN(Number(e))).map(e=>t[e]).join(""):"")),s.trim()||(s="string"==typeof t?t:JSON.stringify(t));let i=o.filter(t=>t.target===e.id&&"providers"===t.targetHandle),a=i.map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="provider").map(e=>e.data.config?.providerId).filter(Boolean),c=o.filter(t=>t.target===e.id&&"tools"===t.targetHandle).map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="tool").map(e=>({id:e.id,type:e.data.config?.toolType,config:e.data.config,status:e.data.config?.connectionStatus||"disconnected"})).filter(Boolean),l=o.filter(t=>t.target===e.id&&"memory"===t.targetHandle).map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="memory");if(0===a.length)throw Error("No AI providers connected to router");let u=i.map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="provider").map(e=>{let t=o.filter(t=>t.target===e.id&&"role"===t.targetHandle).map(e=>{let t=r.find(t=>t.id===e.source);if(t&&"roleAgent"===t.type){let e=t.data.config;return{id:t.id,name:e?.roleName||t.data.label||"Unknown Role",description:e?.roleDescription||e?.customPrompt||"General purpose AI assistant",type:e?.roleType||"custom"}}return null}).filter(Boolean),s=this.checkProviderBrowsingAccess(e.id,r,o);return{id:e.id,providerId:e.data.config?.providerId,modelId:e.data.config?.modelId,roles:t,capabilities:e.data.config?.capabilities||[],hasBrowsingAccess:s,role:t.length>0?t.map(e=>e.name).join(", "):"general_chat"}}),d=await this.getIntelligentRoutingRecommendation(s,t.classification||{},u,c),p=Date.now();try{let e=i.map(e=>r.find(t=>t.id===e.source)).filter(e=>e?.type==="provider").find(e=>e.data.config?.providerId===d.recommendedProvider);if(!e)throw Error(`Recommended provider ${d.recommendedProvider} not found in connected providers`);let o={selectedProvider:d.recommendedProvider,selectedProviderId:e.id,confidence:d.confidence,reason:d.reason,userMessage:s,classification:t.classification||null,availableTools:c.filter(e=>"connected"===e.status),memoryContext:l.length>0,routingContext:{totalProviders:a.length,totalTools:c.length,connectedTools:c.filter(e=>"connected"===e.status).length,hasMemory:l.length>0}};return await n.S.recordRoutingDecision(s,d.recommendedProvider,d.reason,Date.now()-p,!0),o}catch(e){throw await n.S.recordRoutingDecision(s,d.recommendedProvider,d.reason,Date.now()-p,!1),e}}checkProviderBrowsingAccess(e,t,r){return!1}isBrowsingNeeded(e,t,r){return t?.needsBrowsing||r.some(e=>"web_browsing"===e.type)||"string"==typeof e&&(e.toLowerCase().includes("browse")||e.toLowerCase().includes("search")||e.toLowerCase().includes("web"))}getDirectProviderModelId(e,t){if(!e)return"";if("openrouter"===t.toLowerCase())return e;let r=t.toLowerCase(),o=`${r}/`;return e.toLowerCase().startsWith(o)?e.substring(o.length):e}detectWorkflowCapabilities(e){let t=e.filter(e=>"tool"===e.type),r=e.filter(e=>"browsing"===e.type),o=t.map(e=>{let t=e.data.config?.toolType||"unknown";switch(t){case"google_drive":return"Google Drive";case"google_docs":return"Google Docs";case"google_sheets":return"Google Sheets";case"notion":return"Notion";case"calendar":return"Google Calendar";case"gmail":return"Gmail";case"youtube":return"YouTube";case"supabase":return"Database operations";default:return t.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase())}}).filter(e=>"unknown"!==e),s=r.length>0,i="";return(o.length>0||s)&&(i="Available capabilities in this workflow:\n",s&&(i+="- Web browsing and internet access\n"),o.length>0&&(i+=`- Tool integrations: ${o.join(", ")}
`),i+="\nOnly mention these capabilities when specifically asked about them. Do not volunteer this information unless the user asks about what you can do or requests help with these specific tools/browsing."),{tools:o,hasBrowsing:s,capabilities:i}}async executeToolsAndGetResponse(e,t,r,o,s,i,n,a,c,l,u){let d=t.toLowerCase(),m=["google_docs","notion"];if(e.some(e=>m.includes(e))&&(d.includes("story")||d.includes("write a story")||d.includes("create a story")||d.includes("write")&&(d.includes("article")||d.includes("essay")||d.includes("blog")||d.includes("content"))||d.includes("generate")&&(d.includes("content")||d.includes("text")||d.includes("article"))||d.includes("compose")&&(d.includes("letter")||d.includes("email")||d.includes("message"))||d.includes("draft")&&(d.includes("content")||d.includes("article")||d.includes("post"))))return await this.executeContentFirstWorkflow(s,i,n,a,e,[],c,l,u);let g=e.includes("web_browsing")||e.includes("browsing"),h=p(this.getConnectedToolTypes(r,o,g?{needsBrowsing:!0}:void 0)),f="openrouter"===i?n:this.getDirectProviderModelId(n,i);if("google"===i&&a)return await this.callGeminiAPI(s,f,a,c,h,u);if("openai"===i&&a)return await this.callOpenAIAPI(s,f,a,c,h,u);if("anthropic"===i&&a)return await this.callAnthropicAPI(s,f,a,c,h,u);if("deepseek"===i&&a)return await this.callDeepSeekAPI(s,f,a,c,h,u);if("xai"===i&&a)return await this.callXAIAPI(s,f,a,c,h,u);else if("openrouter"===i&&a)return await this.callOpenRouterAPI(s,f,a,c,h,u);else return`I apologize, but I don't have access to the ${i} API or the API key is missing. Please check your configuration.`}async storeDocumentContext(e,t,r,o){try{let i={...o,createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()};await s.H.store("document_context",r,e,t,i,"general",{memoryName:"document_context",maxSize:1024,encryption:!1})}catch(e){}}async getDocumentContext(e,t,r){try{let o=await s.H.retrieve("document_context",r,e,t);if(o)return o;return null}catch(e){return null}}async executeGoogleDocsTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};if(t.id,("continue"===e.toLowerCase().trim()||e.toLowerCase().includes("continue writing")||e.toLowerCase().includes("write the story in full")||e.toLowerCase().includes("finish the story"))&&o&&s){let i=await this.getDocumentContext(o,s,t.id);if(i)try{let{GoogleDocsAPI:n}=await Promise.resolve().then(r.bind(r,79240)),a=await n.getDocument(s,{documentId:i.documentId},6e4);if(a.success){let r=this.extractTextFromGoogleDoc(a.document),c=this.generateContinuationContent(r,e),l=[{insertText:{location:{index:r.length+1},text:"\n\n"+c}}];if((await n.updateDocument(s,{documentId:i.documentId,requests:l},6e4)).success)return await this.storeDocumentContext(o,s,t.id,{...i,lastUpdated:new Date().toISOString()}),{success:!0,document:{documentId:i.documentId},documentUrl:i.documentUrl,title:i.title,content:c,message:`Successfully continued writing in existing Google Doc: "${i.title}"
Document URL: ${i.documentUrl}`,isContinuation:!0}}}catch(e){}}if(e.toLowerCase().includes("write")||e.toLowerCase().includes("create")||e.toLowerCase().includes("story")){let i=this.extractDocumentTitle(e)||"AI Generated Story";try{let{GoogleDocsAPI:n}=await Promise.resolve().then(r.bind(r,79240)),a=await n.createDocument(s,{title:i},6e4);if(a.success&&a.document){let r=a.document.documentId,c=`https://docs.google.com/document/d/${r}/edit`,l=this.generateStoryContent(e);return await n.updateDocument(s,{documentId:r,requests:[{insertText:{location:{index:1},text:l}}]},6e4),o&&s&&await this.storeDocumentContext(o,s,t.id,{documentId:r,documentUrl:c,title:i,type:"google_docs"}),{success:!0,document:a.document,documentUrl:c,title:i,content:l,message:`Successfully created and populated Google Doc: "${i}"
Document URL: ${c}`}}return a}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}try{let{GoogleDocsAPI:t}=await Promise.resolve().then(r.bind(r,79240)),o=this.extractDocumentTitle(e)||"New Document";return await t.createDocument(s,{title:o},6e4)}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}extractDocumentTitle(e){for(let t of[/title[:\s]+"([^"]+)"/i,/called[:\s]+"([^"]+)"/i,/named[:\s]+"([^"]+)"/i,/title[:\s]+'([^']+)'/i,/called[:\s]+'([^']+)'/i,/named[:\s]+'([^']+)'/i,/(?:create|make|new)\s+(?:a\s+)?document\s+(?:called|named|titled)\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,/(?:document|doc)\s+(?:called|named|titled)\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,/(?:title|name):\s*['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,/called\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,/named\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i,/titled\s+['"]?([^'".,\n]+?)['"]?(?:\s|$)/i]){let r=e.match(t);if(r&&r[1]){let e=r[1].trim();if((e=e.replace(/['".,;:!?]+$/,"")).length>0&&e.length<200)return e}}return null}extractTitleFromStory(e){let t=e.split("\n");for(let e=0;e<Math.min(5,t.length);e++){let r=t[e].trim();if(!r)continue;let o=r.match(/^#+\s*(.+)$/);if(o)return o[1].trim();if(r.length>10&&r.length<100&&!r.includes(".")&&/^[A-Z]/.test(r))return r}return null}createSmartChunks(e,t){let r=[],o="";for(let s of e.split(/(\s+|[^\w\s])/))o.length+s.length>t&&o.trim()?o.includes("http")&&!o.includes(" ")||o.includes("**")&&(o.match(/\*\*/g)||[]).length%2==1?o+=s:(r.push(o),o=s):o+=s;return o&&r.push(o),r.filter(e=>e.trim())}createFormattedDocumentRequests(e){let t=[],r=1,o=e.split("\n").filter(e=>e.trim());for(let e=0;e<o.length;e++){let s=o[e].trim();if(!s)continue;let i=0===e||s.startsWith("#"),n=s.replace(/^#+\s*/,"")+"\n\n";t.push({insertText:{location:{index:r},text:n}}),i?t.push({updateTextStyle:{range:{startIndex:r,endIndex:r+n.length-2},textStyle:{bold:!0,fontSize:{magnitude:18,unit:"PT"}},fields:"bold,fontSize"}}):t.push({updateTextStyle:{range:{startIndex:r,endIndex:r+n.length-2},textStyle:{fontSize:{magnitude:11,unit:"PT"}},fields:"fontSize"}}),r+=n.length}return t}async executeContentFirstWorkflow(e,t,o,s,i,n,a,c,l){try{let n=[...e,{role:"system",content:`You are generating content that will be added to a Google Document.

Please generate a complete, well-formatted story based on the user's request.

FORMATTING REQUIREMENTS:
- Start with a compelling title formatted as a markdown heading (# Title)
- Use proper paragraph breaks (separate paragraphs with blank lines)
- Create engaging, well-developed characters and plot
- Include descriptive, immersive language
- End with a satisfying conclusion

STRUCTURE EXAMPLE:
# The Story Title

First paragraph of the story with engaging opening...

Second paragraph continuing the narrative...

Third paragraph developing the plot...

And so on until a satisfying conclusion.

CRITICAL FORMATTING RULES:
- NEVER write the text "[blank line]" - use actual empty lines instead
- NEVER write placeholder text like "[blank line]" or "[empty line]"
- Start with # followed by the title (example: # The Adventure Begins)
- Separate paragraphs with actual blank lines (press Enter twice)
- Generate ONLY the story content - no meta-commentary about documents or tools

EXAMPLE OUTPUT:
# The Mysterious Forest

Sarah stepped into the ancient woods, her heart pounding with anticipation.

The trees whispered secrets in a language she couldn't understand, their branches reaching toward her like gnarled fingers.

As she ventured deeper, the path began to glow with an otherworldly light.`}],c="",u=e=>{c+=e},d="";"google"===t&&s?d=await this.callGeminiAPI(n,o,s,u):"openai"===t&&s&&(d=await this.callOpenAIAPI(n,o,s,u));let p=c||d;p=p.replace(/\[blank line\]/g,"").replace(/\[empty line\]/g,"").replace(/\n\s*\n\s*\n/g,"\n\n").trim();let m=[];if(i.includes("google_docs")&&l){let{GoogleDocsAPI:e}=await Promise.resolve().then(r.bind(r,79240)),t=this.extractTitleFromStory(p)||"AI Generated Story",o=await e.createDocument(l,{title:t},6e4);if(o.success&&o.document){let r=o.document.documentId,s=`https://docs.google.com/document/d/${r}/edit`,i=this.createFormattedDocumentRequests(p);await e.updateDocument(l,{documentId:r,requests:i},6e4),m.push({type:"Google Docs",title:t,url:s,success:!0})}}if(i.includes("notion")&&l)try{let{NotionAPI:e}=await Promise.resolve().then(r.bind(r,67920)),t=this.extractTitleFromStory(p)||"AI Generated Story",o=await e.createPage(l,{parent:{type:"page_id",page_id:"default"},properties:{title:{title:[{text:{content:t}}]}},children:[{object:"block",type:"paragraph",paragraph:{rich_text:[{text:{content:p}}]}}]},6e4);o.success&&m.push({type:"Notion",title:t,url:o.page?.url||"Notion page created",success:!0})}catch(e){m.push({type:"Notion",success:!1,error:e instanceof Error?e.message:"Unknown error"})}let g="";if(m.length>0){g="\n\n---\n\n";let e=m.filter(e=>e.success),t=m.filter(e=>!e.success);e.length>0&&(g+="✅ **Documents Created Successfully!**\n\n",e.forEach(e=>{g+=`📄 **${e.type}:** ${e.title}
🔗 **Link:** ${e.url}

`})),t.length>0&&(g+="⚠️ **Some documents could not be created:**\n\n",t.forEach(e=>{g+=`❌ **${e.type}:** ${e.error}

`})),g+="Your content has been saved and is ready for editing and sharing!"}let h=`${p}${g}`;if(a)for(let e of this.createSmartChunks(h,15))a(e),await new Promise(e=>setTimeout(e,50));return h}catch(t){let e=`I encountered an error while generating your content and creating the documents. Let me try a different approach.

Error: ${t instanceof Error?t.message:"Unknown error"}`;if(a)for(let t of this.createSmartChunks(e,15))a(t),await new Promise(e=>setTimeout(e,50));return e}}generateContinuationContent(e,t){let r=e.split("\n\n").pop()||"";return r.includes("[Continue writing")||r.endsWith("...")||!r.endsWith(".")&&!r.endsWith("!")&&!r.endsWith("?")?`The story continues from where it left off...

**Chapter 2: The Development Phase**

David's journey into the world of AI routing began with a simple observation: developers were wasting countless hours switching between different AI providers, manually testing which one worked best for their specific use cases. The inefficiency was staggering.

Working late into the night from his modest apartment, David began coding what would eventually become RouKey. The initial prototype was rough around the edges, but it demonstrated a revolutionary concept - intelligent routing that could automatically select the best AI provider based on the task at hand.

**The Breakthrough Moment**

The breakthrough came when David realized that the key wasn't just in routing requests, but in understanding the context and requirements of each query. He developed a sophisticated classification system that could analyze user inputs and match them with the most suitable AI provider.

This wasn't just another API wrapper - this was a complete paradigm shift in how developers could interact with AI services.

**Building the Foundation**

As the months passed, David refined his system, adding features like:
- Smart load balancing across multiple providers
- Automatic failover mechanisms
- Cost optimization algorithms
- Real-time performance monitoring

Each feature was born from real-world needs, tested rigorously, and implemented with the precision of someone who understood both the technical challenges and the business implications.

The RouKey platform was taking shape, and with it, David's vision of democratizing AI access for developers worldwide.

**The Launch**

Today marks a significant milestone in this journey. RouKey is no longer just a vision or a prototype - it's a fully functional platform ready to transform how developers work with AI.

The story continues as David prepares to share his creation with the world, knowing that this is just the beginning of a much larger revolution in AI accessibility and efficiency.`:`

**Continuing the Story**

The narrative continues with new developments and deeper insights into David's revolutionary journey with RouKey...

As the platform evolved, David faced new challenges and opportunities that would shape the future of AI routing technology.`}extractTextFromGoogleDoc(e){if(!e||!e.body||!e.body.content)return"";let t="";for(let r of e.body.content)if(r.paragraph)for(let e of r.paragraph.elements||[])e.textRun&&(t+=e.textRun.content||"");return t.trim()}generateStoryContent(e){let t=this.extractStoryTheme(e);return`# ${t}

**Chapter 1: The Beginning**

In the heart of innovation, where technology meets human ambition, a story unfolds that would change the landscape of artificial intelligence forever.

David Okoro, a visionary developer from Nigeria, sat in his modest workspace, surrounded by the glow of multiple monitors displaying lines of code that would soon revolutionize how developers interact with AI systems. The idea for RouKey had been brewing in his mind for months - a sophisticated routing system that could intelligently distribute AI requests across multiple providers, optimizing for performance, cost, and reliability.

**The Vision Takes Shape**

The problem was clear: developers were struggling with the complexity of managing multiple AI providers, each with their own strengths and limitations. Some excelled at creative writing, others at code generation, and still others at data analysis. But switching between them manually was inefficient and error-prone.

David's solution was elegant in its simplicity yet revolutionary in its execution. RouKey would act as an intelligent gateway, analyzing each request and automatically routing it to the most suitable AI provider. But this was just the beginning.

**Building the Foundation**

Working tirelessly through the nights, David began implementing the core architecture:

- **Smart Classification System**: Using advanced algorithms to understand the nature and requirements of each request
- **Dynamic Load Balancing**: Distributing requests efficiently across multiple providers
- **Automatic Failover**: Ensuring reliability even when individual providers experienced issues
- **Cost Optimization**: Selecting providers based on both performance and cost considerations

**The Breakthrough**

The breakthrough moment came when David realized that RouKey could do more than just route requests - it could learn from patterns, optimize over time, and even predict the best provider for specific types of tasks before the request was made.

This wasn't just another API wrapper. This was the future of AI interaction.

**Looking Forward**

As the platform took shape, David knew he was building something that would empower developers worldwide, democratizing access to the best AI capabilities regardless of which specific provider offered them.

The story of RouKey was just beginning...

---

*This document was created by RouKey AI Assistant on ${new Date().toLocaleDateString()}*
*Continue editing and expanding this story as needed*`}extractStoryTheme(e){let t=e.toLowerCase();if(t.includes("roukey")||t.includes("ai routing")||t.includes("david"))return"The RouKey Story: A Revolution in AI Routing";if(t.includes("adventure"))return"An Epic Adventure";if(t.includes("mystery"))return"A Mysterious Tale";if(t.includes("romance"))return"A Love Story";if(t.includes("sci-fi")||t.includes("science fiction"))return"A Science Fiction Epic";else if(t.includes("fantasy"))return"A Fantasy Adventure";else if(t.includes("horror"))return"A Chilling Horror Story";else if(t.includes("comedy"))return"A Comedy Story";else if(t.includes("drama"))return"A Dramatic Tale";else return"An AI-Generated Story"}async executeGoogleDriveTool(e,t,r,o){return{success:!1,error:"Google Drive tool execution not yet implemented"}}async executeGoogleSheetsTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeGoogleSheets(t.data.config,n);return{success:!0,result:e.data,message:"Google Sheets operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeGmailTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeGmail(t.data.config,n);return{success:!0,result:e.data,message:"Gmail operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeGoogleCalendarTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeGoogleCalendar(t.data.config,n);return{success:!0,result:e.data,message:"Google Calendar operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeYouTubeTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeYouTube(t.data.config,n);return{success:!0,result:e.data,message:"YouTube operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeNotionTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeNotion(t.data.config,n);return{success:!0,result:e.data,message:"Notion operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeSupabaseTool(e,t,o,s){let{ToolExecutor:i}=await r.e(2958).then(r.bind(r,82958));if(!s)return{success:!1,error:"User ID is required for tool execution"};let n={userInput:e,workflowId:o||"unknown-workflow",nodeId:t.id,userId:s};try{let e=await i.executeSupabase(t.data.config,n);return{success:!0,result:e.data,message:"Supabase operation completed successfully"}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async executeProviderNode(e,t,r,o,s,i,n,a){let c=e.data.config,l=c?.providerId,u=c?.modelId,d=c?.apiKey;if(!l||!u)throw Error("AI Provider node not properly configured");let m=t?.classification?.needsTools||t?.needsTools,g=t?.classification?.toolTypes||t?.toolTypes||[],h="";"string"==typeof t?h=t:"object"==typeof t&&((h=t.userMessage||t.message||t.input||t.content)||"string"!=typeof t["0"]||"string"!=typeof t["1"]||(h=Object.keys(t).filter(e=>!isNaN(Number(e))).map(e=>t[e]).join("")),h||(h=JSON.stringify(t))),"continue"===h.toLowerCase().trim()&&r&&r.length;let f=s?this.detectWorkflowCapabilities(s):{tools:[],hasBrowsing:!1,capabilities:""},y=[];if(f.capabilities&&y.push({role:"system",content:f.capabilities}),r&&r.length>0){let e=r.map(e=>({role:e.role,content:"string"==typeof e.content?e.content:Array.isArray(e.content)&&e.content[0]?.text?e.content[0].text:JSON.stringify(e.content)}));y.push(...e);let t=y[y.length-1];t&&t.content===h||y.push({role:"user",content:h})}else y.push({role:"user",content:h});let w="";try{if(m&&g.length>0&&s&&i)w=await this.executeToolsAndGetResponse(g,h,s,i,y,l,u,d,o,n,a);else{let e=this.getConnectedToolTypes(s,i,t.classification),r=p(e),n="openrouter"===l?u:this.getDirectProviderModelId(u,l);if("google"===l&&d)w=await this.callGeminiAPI(y,n,d,o,r,a);else if("openai"===l&&d)w=await this.callOpenAIAPI(y,n,d,o,r,a);else if("anthropic"===l&&d)w=await this.callAnthropicAPI(y,n,d,o,r,a);else if("deepseek"===l&&d)w=await this.callDeepSeekAPI(y,n,d,o,r,a);else if("xai"===l&&d)w=await this.callXAIAPI(y,n,d,o,r,a);else if("openrouter"===l&&d)w=await this.callOpenRouterAPI(y,n,d,o,r,a);else if(w=`Hello! I'm a ${l} AI assistant using ${u}. I received your message: "${h}". This is a placeholder response as the API integration is not yet implemented for this provider.`,o){let e=w.split(" ");for(let t=0;t<e.length;t++){let r=(0===t?"":" ")+e[t];o(r),await new Promise(e=>setTimeout(e,50))}}}}catch(e){if(w="I apologize, but I encountered an error while processing your request. Please try again later.",o){let e=w.split(" ");for(let t=0;t<e.length;t++)o((0===t?"":" ")+e[t]),await new Promise(e=>setTimeout(e,50))}}return{provider:l,model:u,response:w,metadata:{inputType:typeof t,hasApiKey:!!d,timestamp:new Date().toISOString(),tokenCount:w.length/4}}}getConnectedToolTypes(e,t,r){if(!e||!t)return[];let o=e.filter(e=>"tool"===e.type),s=[];for(let e of o)t.some(t=>t.source===e.id||t.target===e.id)&&e.data?.config?.toolType&&e.data?.config?.isAuthenticated&&s.push(e.data.config.toolType);return s}async callGeminiAPI(e,t,r,o,s,i){let n=t.includes("/")?t.split("/").pop():t,a=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Workflow)",Origin:"https://rokey.app"},body:JSON.stringify({model:n,messages:e,temperature:.7,max_tokens:1e3,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({type:"function",function:e})),tool_choice:"auto"}})});if(!a.ok)throw Error(`Gemini API error: ${a.status} ${a.statusText}`);if(o&&a.body){let n=a.body.getReader(),c=new TextDecoder,l="",u="",d=[],p=!1;try{for(;;){let{done:e,value:t}=await n.read();if(e)break;let r=c.decode(t,{stream:!0}),s=(u+=r).split("\n");for(let e of(u=s.pop()||"",s))if(e.startsWith("data: ")){let t=e.slice(6).trim();if("[DONE]"===t||!t)continue;try{let e=JSON.parse(t),r=e.choices?.[0]?.delta;if(r?.tool_calls){for(let e of(p=!0,r.tool_calls))if("function"===e.type){let t=d.find(t=>t.index===e.index);t?e.function?.arguments&&(t.arguments+=e.function.arguments):(t={index:e.index,name:e.function?.name||"",arguments:e.function?.arguments||""},d.push(t))}}let s=r?.content||"";s&&!p?(l+=s,o(s)):s&&(l+=s)}catch(e){}}}if(u.trim()){let e=u.trim();if(e.startsWith("data: ")){let t=e.slice(6).trim();if(t&&"[DONE]"!==t)try{let e=JSON.parse(t),r=e.choices?.[0]?.delta?.content||"";r&&!p?(l+=r,o(r)):r&&(l+=r)}catch(e){}}}}finally{n.releaseLock()}if(p&&d.length>0&&i&&s){let s=new f(i),n=y(await s.executeFunctionCalls(d.map(e=>({name:e.name,arguments:e.arguments})))),a=[...e,{role:"assistant",content:l},{role:"system",content:`Function call results:
${n}

IMPORTANT: If the user requested multiple actions (like "delete and create"), continue with the remaining tasks. Don't stop after completing just one part of the request.`}];return o&&o(`✅ Successfully executed ${d.length} function${d.length>1?"s":""}. Generating response...

`),await this.callGeminiAPI(a,t,r,o)}return l||"No response generated"}{let n=await a.json();if(w(n)&&i&&s){let s=b(n),a=new f(i),c=y(await a.executeFunctionCalls(s)),l=[...e,{role:"assistant",content:n.choices?.[0]?.message?.content||""},{role:"system",content:`Function call results:
${c}

IMPORTANT: If the user requested multiple actions (like "delete and create"), continue with the remaining tasks. Don't stop after completing just one part of the request.`}];return await this.callGeminiAPI(l,t,r,o)}return n.choices?.[0]?.message?.content||"No response generated"}}async callOpenAIAPI(e,t,r,o,s,i){let n=t.includes("/")?t.split("/").pop():t,a=await fetch("https://api.openai.com/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},body:JSON.stringify({model:n,messages:e,temperature:.7,max_tokens:1e3,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({type:"function",function:e})),tool_choice:"auto"}})});if(!a.ok)throw Error(`OpenAI API error: ${a.status} ${a.statusText}`);if(o&&a.body)return await this.handleOpenAICompatibleStreaming(a,e,t,r,o,s,i,"OpenAI",this.callOpenAIAPI.bind(this));{let n=await a.json();if(w(n)&&i&&s){let s=b(n),a=new f(i),c=y(await a.executeFunctionCalls(s)),l=[...e,{role:"assistant",content:n.choices?.[0]?.message?.content||""},{role:"system",content:`Function call results:
${c}`}];return await this.callOpenAIAPI(l,t,r,o)}return n.choices?.[0]?.message?.content||"No response generated"}}async callAnthropicAPI(e,t,r,o,s,i){let n=t.includes("/")?t.split("/").pop():t,a=await fetch("https://api.anthropic.com/v1/messages",{method:"POST",headers:{"Content-Type":"application/json","x-api-key":r,"anthropic-version":"2023-06-01",Origin:"https://rokey.app","User-Agent":"RoKey/1.0 (Workflow)"},body:JSON.stringify({model:n,max_tokens:1e3,messages:e,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({name:e.name,description:e.description,input_schema:e.parameters}))}})});if(!a.ok)throw Error(`Anthropic API error: ${a.status} ${a.statusText}`);if(o&&a.body){let n=a.body.getReader(),c=new TextDecoder,l="",u="",d=[],p=!1;try{for(;;){let{done:e,value:t}=await n.read();if(e)break;let r=c.decode(t,{stream:!0}),s=(u+=r).split("\n");for(let e of(u=s.pop()||"",s))if(e.startsWith("data: ")){let t=e.slice(6).trim();if("[DONE]"===t||!t)continue;try{let e=JSON.parse(t),r=e.delta?.text||"";if(r&&(l+=r,o(r)),e.delta?.tool_use){p=!0;let t=e.delta.tool_use;d.push({name:t.name,arguments:JSON.stringify(t.input||{})})}}catch(e){}}}if(u.trim()){let e=u.trim();if(e.startsWith("data: ")){let t=e.slice(6).trim();if(t&&"[DONE]"!==t)try{let e=JSON.parse(t),r=e.delta?.text||"";r&&(l+=r,o(r))}catch(e){}}}}finally{n.releaseLock()}if(p&&d.length>0&&i&&s){let s=new f(i),n=y(await s.executeFunctionCalls(d)),a=[...e,{role:"assistant",content:l},{role:"system",content:`Function call results:
${n}`}];return await this.callAnthropicAPI(a,t,r,o)}return l||"No response generated"}{let n=await a.json();if(n.content&&i&&s){let s=n.content.filter(e=>"tool_use"===e.type);if(s.length>0){let a=s.map(e=>({name:e.name,arguments:e.input})),c=new f(i),l=y(await c.executeFunctionCalls(a)),u=[...e,{role:"assistant",content:n.content?.[0]?.text||""},{role:"user",content:`Function call results:
${l}`}];return await this.callAnthropicAPI(u,t,r,o)}}return n.content?.[0]?.text||"No response generated"}}async callDeepSeekAPI(e,t,r,o,s,i){let n=t.includes("/")?t.split("/").pop():t,a=await fetch("https://api.deepseek.com/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Origin:"https://rokey.app","User-Agent":"RoKey/1.0 (Workflow)"},body:JSON.stringify({model:n,messages:e,temperature:.7,max_tokens:1e3,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({type:"function",function:e})),tool_choice:"auto"}})});if(!a.ok)throw Error(`DeepSeek API error: ${a.status} ${a.statusText}`);if(o&&a.body)return await this.handleOpenAICompatibleStreaming(a,e,t,r,o,s,i,"DeepSeek",this.callDeepSeekAPI.bind(this));{let n=await a.json();if(w(n)&&i&&s){let s=b(n),a=new f(i),c=y(await a.executeFunctionCalls(s)),l=[...e,{role:"assistant",content:n.choices?.[0]?.message?.content||""},{role:"system",content:`Function call results:
${c}`}];return await this.callDeepSeekAPI(l,t,r,o)}return n.choices?.[0]?.message?.content||"No response generated"}}async callXAIAPI(e,t,r,o,s,i){let n=t.includes("/")?t.split("/").pop():t,a=await fetch("https://api.x.ai/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Origin:"https://rokey.app","User-Agent":"RoKey/1.0 (Workflow)"},body:JSON.stringify({model:n,messages:e,temperature:.7,max_tokens:1e3,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({type:"function",function:e})),tool_choice:"auto"}})});if(!a.ok)throw Error(`XAI API error: ${a.status} ${a.statusText}`);if(o&&a.body)return await this.handleOpenAICompatibleStreaming(a,e,t,r,o,s,i,"XAI",this.callXAIAPI.bind(this));{let n=await a.json();if(w(n)&&i&&s){let s=b(n),a=new f(i),c=y(await a.executeFunctionCalls(s)),l=[...e,{role:"assistant",content:n.choices?.[0]?.message?.content||""},{role:"system",content:`Function call results:
${c}`}];return await this.callXAIAPI(l,t,r,o)}return n.choices?.[0]?.message?.content||"No response generated"}}async callOpenRouterAPI(e,t,r,o,s,i){let n=await fetch("https://openrouter.ai/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Origin:"https://rokey.app","User-Agent":"RoKey/1.0 (Workflow)","HTTP-Referer":"https://rokey.app","X-Title":"RouKey Workflow"},body:JSON.stringify({model:t,messages:e,temperature:.7,max_tokens:1e3,stream:!!o,...s&&s.length>0&&{tools:s.map(e=>({type:"function",function:e})),tool_choice:"auto"}})});if(!n.ok){let e=n.statusText;try{let t=await n.json();e=t?.error?.message||t?.message||n.statusText}catch(e){}throw Error(`OpenRouter API error: ${n.status} ${e}`)}if(o&&n.body)return await this.handleOpenAICompatibleStreaming(n,e,t,r,o,s,i,"OpenRouter",this.callOpenRouterAPI.bind(this));{let a=await n.json();if(w(a)&&i&&s){let s=b(a),n=new f(i),c=y(await n.executeFunctionCalls(s)),l=[...e,{role:"assistant",content:a.choices?.[0]?.message?.content||""},{role:"system",content:`Function call results:
${c}`}];return await this.callOpenRouterAPI(l,t,r,o)}return a.choices?.[0]?.message?.content||"No response generated"}}async callProviderAPI(e,t,r,o,s){let i=[{role:"user",content:t}];switch(e){case"google":return await this.callGeminiAPI(i,r,o,s);case"openai":return await this.callOpenAIAPI(i,r,o,s);case"anthropic":return await this.callAnthropicAPI(i,r,o,s);case"deepseek":return await this.callDeepSeekAPI(i,r,o,s);case"xai":return await this.callXAIAPI(i,r,o,s);case"openrouter":return await this.callOpenRouterAPI(i,r,o,s);default:throw Error(`Unsupported provider: ${e}`)}}async executePlannerNode(e,t){let r=e.data.config,o="string"==typeof t?t:"Plan browsing task",s=r?.maxSubtasks||5;if(r?.providerId&&r?.modelId)try{let e=await this.getApiKeyForProvider(r.providerId);if(e){let t=`Create a detailed browsing plan for this task: "${o}"

Generate a JSON plan with ${s} subtasks. Each subtask should have:
- id: unique identifier
- type: 'search', 'navigate', 'analyze_results', or 'check_completion'
- description: what this step accomplishes
- target: what to search for or navigate to
- parameters: { extractionGoal: specific goal for this step }

Focus on efficient information gathering and logical progression.

Respond with JSON only:
{
  "subtasks": [
    {
      "id": "step_1",
      "type": "search",
      "description": "...",
      "target": "...",
      "parameters": { "extractionGoal": "..." }
    }
  ]
}`,i=await this.callProviderAPI(r.providerId,t,r.modelId,e);try{let e=i.replace(/```json\n?/g,"").replace(/```\n?/g,"").trim(),t=JSON.parse(e);return{id:`planner_${Date.now()}`,task:o,subtasks:t.subtasks.map((e,t)=>({...e,status:"pending",order:t+1})),estimatedTime:Math.min(2*t.subtasks.length,10),priority:"medium",timestamp:new Date().toISOString(),generatedBy:`${r.providerId}:${r.modelId}`}}catch(e){}}}catch(e){}return{id:`planner_${Date.now()}`,task:o,subtasks:[{id:"search_primary",type:"search",description:`Primary search for: ${o}`,target:o,status:"pending",parameters:{extractionGoal:"Find relevant websites and initial information"}},{id:"analyze_results",type:"analyze_results",description:"Analyze search results and select best websites",target:"search_results",status:"pending",parameters:{extractionGoal:"Select top websites based on relevance and authority"}}].slice(0,s),estimatedTime:Math.min(2*s,10),priority:"medium",timestamp:new Date().toISOString(),generatedBy:"static_fallback"}}getExecutionStatus(e){return this.activeExecutions.get(e)||null}getActiveExecutions(){return Array.from(this.activeExecutions.values())}async executeClassifierNode(e,t,r,o,s,i){e.data.config;let n="string"==typeof t?t:t?.content||t?.prompt||"";if(!n.trim())throw Error("No user input provided for classification");let a=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!a)throw Error("Classification service unavailable - no API key");let c=o.filter(t=>t.source===e.id&&r.find(e=>e.id===t.target)?.type==="centralRouter");if(0===c.length)throw Error("Classifier must be connected to a Central Router");let l=r.find(e=>e.id===c[0].target),u=this.getAvailableRolesFromRouter(l,r,o),d=this.getAvailableToolsFromWorkflow(r,o);i&&i.length>0&&i.filter(e=>{if("user"!==e.role&&"assistant"!==e.role)return!1;let t="string"==typeof e.content?e.content:Array.isArray(e.content)&&e.content[0]?.text?e.content[0].text:JSON.stringify(e.content);return!(t.includes("Function call results:")||t.includes("Successfully executed")||t.includes('"success":')||t.includes('"documentId":'))}).map(e=>({role:e.role,content:"string"==typeof e.content?e.content:Array.isArray(e.content)&&e.content[0]?.text?e.content[0].text:JSON.stringify(e.content)}));let p=await this.performComprehensiveClassification(n,u,a,d,void 0,void 0);return{...t,classification:p,classifiedRoles:p.roles,needsBrowsing:p.needsBrowsing,needsTools:p.needsTools,toolTypes:p.toolTypes,isMultiRole:p.isMultiRole,reasoning:p.reasoning}}async executeToolNode(e,t,r,o){let s=e.data.config;if(!s.toolType)throw Error("Tool type not configured");return"web_browsing"===s.toolType?{...t,toolResult:{type:"web_browsing",status:"delegated_to_browsing_node",message:"Web browsing handled by dedicated browsing node"}}:{...t,toolResult:{type:s.toolType,status:"not_implemented",message:`${s.toolType} integration coming soon`}}}async executeOutputNode(e,t,r,o){let s,i=e.data.config,n=i.outputFormat||"stream",a=i.chunkSize||15;if("string"==typeof t)s=t;else if(t?.response)s=t.response;else if(t?.content)s=t.content;else if(t?.result)s=t.result;else if(t?.finalOutput)s=t.finalOutput;else try{if("object"==typeof t&&null!==t){let e=this.extractResponseFromNestedObject(t);s=e||JSON.stringify(t)}else s="Workflow completed successfully."}catch(e){s="Workflow completed successfully."}return{...t,finalOutput:s,outputConfig:{format:n,chunkSize:a,timestamp:new Date().toISOString()},isComplete:!0}}async handleOpenAICompatibleStreaming(e,t,r,o,s,i,n,a="OpenAI",c){let l=e.body.getReader(),u=new TextDecoder,d="",p="",m=[],g=!1;try{for(;;){let{done:e,value:t}=await l.read();if(e)break;let r=u.decode(t,{stream:!0}),o=(p+=r).split("\n");for(let e of(p=o.pop()||"",o))if(e.startsWith("data: ")){let t=e.slice(6).trim();if("[DONE]"===t||!t)continue;try{let e=JSON.parse(t),r=e.choices?.[0]?.delta,o=r?.content||"";if(o&&(d+=o,s(o)),r?.tool_calls){for(let e of(g=!0,r.tool_calls))if("function"===e.type){let t=m.find(t=>t.index===e.index);t?e.function?.arguments&&(t.arguments+=e.function.arguments):(t={index:e.index,name:e.function?.name||"",arguments:e.function?.arguments||""},m.push(t))}}}catch(e){}}}if(p.trim()){let e=p.trim();if(e.startsWith("data: ")){let t=e.slice(6).trim();if(t&&"[DONE]"!==t)try{let e=JSON.parse(t),r=e.choices?.[0]?.delta?.content||"";r&&(d+=r,s(r))}catch(e){}}}}finally{l.releaseLock()}if(g&&m.length>0&&n&&i){let e=new f(n),i=y(await e.executeFunctionCalls(m.map(e=>({name:e.name,arguments:e.arguments})))),a=[...t,{role:"assistant",content:d},{role:"system",content:`Function call results:
${i}`}];return await c(a,r,o,s)}return d||"No response generated"}fixCommonJsonIssues(e){let t=e;return(t=(t=(t=(t=(t=(t=t.replace(/,(\s*[}\]])/g,"$1")).replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g,'$1"$2":')).replace(/,\s*}/g,"}")).replace(/,\s*]/g,"]")).replace(/[\x00-\x1F\x7F]/g,"")).replace(/"([^"]*)"(\s*:\s*)"([^"]*)"([^"]*)"([^"]*)"([^",}]*)/g,'"$1"$2"$3\\"$4\\"$5"')).replace(/:\s*"([^"]*)"([^",}]*)"([^",}]*)/g,(e,t,r,o)=>!r.trim()||r.includes(",")||r.includes("}")?e:`: "${t}\\"${r}\\"${o}"`)}async parseJsonWithAI(e){let t=this.getClassificationApiKeys();if(0===t.length)throw Error("No API keys available for AI JSON parsing");let r=`You are a JSON parser and fixer. Your task is to take malformed JSON content and return valid, properly formatted JSON.

Rules:
1. Extract the JSON object from any surrounding text or markdown
2. Fix common JSON issues: unescaped quotes, trailing commas, missing quotes around keys
3. Preserve the original data structure and values as much as possible
4. Return ONLY the valid JSON object, no explanations or markdown formatting
5. Use double quotes for all strings and property names
6. Ensure proper escaping of quotes within string values

Input content to fix:
${e}

Return the fixed JSON:`;for(let e=0;e<t.length;e++){let o=t[e];try{let e=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key="+o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:r}]}],generationConfig:{temperature:.2,maxOutputTokens:2048,topP:.8,topK:10}})});if(!e.ok)throw Error(`HTTP ${e.status}: ${e.statusText}`);let t=await e.json(),s=t.candidates?.[0]?.content?.parts?.[0]?.text;if(!s)throw Error("No response from AI");let i=s.trim();i.startsWith("```json")?i=i.replace(/^```json\s*/,"").replace(/\s*```$/,""):i.startsWith("```")&&(i=i.replace(/^```\s*/,"").replace(/\s*```$/,""));let n=i.match(/\{[\s\S]*\}/);return n&&(i=n[0]),JSON.parse(i)}catch(r){if(e===t.length-1)throw r}}throw Error("All AI JSON parsing attempts failed")}async parseJsonRobustly(e){try{return await this.parseJsonWithAI(e)}catch(e){}try{return JSON.parse(e)}catch(e){}try{let t=e.replace(/```json\n?/g,"").replace(/```\n?/g,"").trim(),r=t.match(/\{[\s\S]*\}/);return r&&(t=r[0]),t=this.fixCommonJsonIssues(t),JSON.parse(t)}catch(e){}try{let t=e.replace(/```json\n?/gi,"").replace(/```\n?/gi,"").replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"\r").trim(),r=t.indexOf("{"),o=t.lastIndexOf("}");return -1!==r&&-1!==o&&o>r&&(t=t.substring(r,o+1)),t=this.fixCommonJsonIssues(t),JSON.parse(t)}catch(e){}try{let t=e.replace(/```json\n?/gi,"").replace(/```\n?/gi,"").trim(),r=t.indexOf("{");if(-1!==r){t=t.substring(r);let e=0,o=-1;for(let r=0;r<t.length;r++)if("{"===t[r]&&e++,"}"===t[r]&&(e--,0===e)){o=r;break}-1!==o&&(t=t.substring(0,o+1))}return t=t.replace(/\\"/g,'"').replace(/:\s*"([^"]*)"([^",}]*)"([^",}]*)"([^",}]*)/g,': "$1\\"$2\\"$3"').replace(/,(\s*[}\]])/g,"$1").replace(/"\s*"([a-zA-Z_])/g,'", "$1').replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g,'$1"$2":').replace(/[\x00-\x1F\x7F]/g,""),JSON.parse(t)}catch(e){}try{let t=e.replace(/```json\n?/gi,"").replace(/```\n?/gi,"").trim(),r=t.match(/\{[\s\S]*\}/);return r&&(t=r[0]),t=(t=t.replace(/("reason"\s*:\s*"[^"]*)"([^"]*)"([^"]*)"([^"]*)/g,'$1\\"$2\\"$3')).replace(/("[\w_]+"\s*:\s*"[^"]*)"([^",}]*)"([^",}]*)/g,(e,t,r,o)=>!r.trim()||r.includes(",")||r.includes("}")||r.includes("]")?e:`${t}\\"${r}\\"${o}`),t=this.fixCommonJsonIssues(t),JSON.parse(t)}catch(e){}try{if(e.includes("providerId")&&e.includes("confidence")){let t=e.match(/["']?providerId["']?\s*:\s*["']([^"']+)["']/),r=e.match(/["']?confidence["']?\s*:\s*([0-9.]+)/),o=e.match(/["']?reason["']?\s*:\s*["']([^"']*(?:\\"[^"']*)*[^"']*)["']/);if(t)return{providerId:t[1],confidence:r?parseFloat(r[1]):.8,reason:o?o[1].replace(/\\"/g,'"'):"Manually reconstructed from malformed JSON"}}if(e.includes("isMultiRole")&&e.includes("needsTools")){let t=e.match(/["']?isMultiRole["']?\s*:\s*(true|false)/),r=e.match(/["']?needsTools["']?\s*:\s*(true|false)/),o=e.match(/["']?needsBrowsing["']?\s*:\s*(true|false)/);return{isMultiRole:!!t&&"true"===t[1],roles:[{roleId:"fallback-role",confidence:.5,executionOrder:1}],needsBrowsing:!!o&&"true"===o[1],needsTools:!r||"true"===r[1],needsVision:!1,toolTypes:["google_docs"],reasoning:"Manually reconstructed from malformed JSON"}}}catch(e){}throw Error("All JSON parsing strategies failed")}async getIntelligentRoutingRecommendation(e,t,r,o){let s=this.getClassificationApiKeys();if(0===s.length)return{recommendedProvider:r[0]?.providerId||"google",confidence:.5,reason:"Fallback routing - no AI classification available"};let i=this.isBrowsingNeeded(e,t,o),n=`You are RouKey's intelligent central router. Analyze the user request and classification data to select the best AI provider.

Available Providers:
${r.map(e=>{let t=e.roles&&e.roles.length>0?e.roles.map(e=>`${e.name} (${e.description.substring(0,100)})`).join("; "):"General Chat",r=e.hasBrowsingAccess?"✅ HAS BROWSING ACCESS":"❌ NO BROWSING ACCESS";return`- Provider: ${e.providerId} (Model: ${e.modelId}, Roles: ${t}, Browsing: ${r}, Node ID: ${e.id})`}).join("\n")}

Available Tools:
${o.map(e=>`- ${e.type} (Status: ${e.status})`).join("\n")}

${i?'\uD83C\uDF10 BROWSING REQUIRED: This request needs web browsing capabilities. STRONGLY PRIORITIZE providers with "✅ HAS BROWSING ACCESS".':""}

Consider:
1. Task complexity and requirements
2. Provider capabilities and specializations
3. Connected role specializations and descriptions
4. Tool integration needs
5. Classification insights
6. ${i?"CRITICAL: Browsing access requirement - providers without browsing access cannot fulfill this request":"No special browsing requirements"}

IMPORTANT: Use the Provider ID (e.g., "google", "openrouter") NOT the Node ID (e.g., "provider-1751826546217").

Respond with JSON: {"providerId": "provider_id", "confidence": 0.95, "reason": "detailed explanation"}`,a=`User Request: "${e}"

Classification Data:
${JSON.stringify(t,null,2)}

Select the most appropriate provider for this request.`;for(let e=0;e<s.length;e++){let t=s[e],o=0===e?"primary":`fallback${e}`;try{let e=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key="+t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{role:"user",parts:[{text:n+"\n\n"+a}]}],generationConfig:{temperature:.1,maxOutputTokens:500}})});if(!e.ok)if(429===e.status){Error(`Rate limit on ${o} key`);continue}else throw Error(`Routing API error: ${e.status}`);let s=await e.json(),i=s.candidates?.[0]?.content?.parts?.[0]?.text;if(i)try{let e=await this.parseJsonRobustly(i);if(r.some(t=>t.providerId===e.providerId))return{recommendedProvider:e.providerId,confidence:Math.min(Math.max(e.confidence||.8,0),1),reason:e.reason||"AI-based routing decision"};throw Error(`Provider validation failed: ${e.providerId} not found in [${r.map(e=>e.providerId).join(", ")}]`)}catch(e){throw e}}catch(t){if(t instanceof Error||Error("Unknown error"),e===s.length-1)break;continue}}let c=this.isBrowsingNeeded(e,t,o),l=r[0],u="Fallback routing - AI routing failed";if(c){let e=r.filter(e=>e.hasBrowsingAccess);e.length>0?(l=e[0],u="Smart fallback - selected provider with browsing access"):u="Fallback routing - browsing needed but no providers have browsing access"}return{recommendedProvider:l?.providerId||"google",confidence:c&&l?.hasBrowsingAccess?.7:.6,reason:u}}extractResponseFromNestedObject(e){if(!e||"object"!=typeof e)return null;if(e.response&&"string"==typeof e.response||e.provider&&e.response)return e.response;if(e.finalOutput&&"string"==typeof e.finalOutput)return e.finalOutput;for(let t in e)if(e.hasOwnProperty(t)&&"object"==typeof e[t]){let r=this.extractResponseFromNestedObject(e[t]);if(r)return r}return null}async executeVisionNode(e,t,r,o,s){let i=e.data.config;if(!i.providerId||!i.modelId)throw Error("Vision node not properly configured - missing provider or model");if(!this.detectImagesInInput(t))return{...t,visionResult:{status:"no_images",message:"No images found in input"}};try{let e=await this.getApiKeyForProvider(i.providerId);if(!e)throw Error(`No API key available for provider: ${i.providerId}`);let r="string"==typeof t?t:t?.content||t?.prompt||"Analyze this image",o=this.extractImagesFromInput(t),n=`${r}

Please analyze the provided image(s) and provide a detailed description of what you see.`,a=await this.callProviderAPI(i.providerId,n,i.modelId,e,s);return{...t,visionResult:{status:"processed",analysis:a,provider:i.providerId,model:i.modelId,imageCount:o.length,timestamp:new Date().toISOString()},visionProcessed:!0,response:a}}catch(e){return{...t,visionResult:{status:"error",message:`Vision processing failed: ${e instanceof Error?e.message:"Unknown error"}`,provider:i.providerId,model:i.modelId}}}}detectImagesInInput(e){return"string"==typeof e?/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(e)||e.includes("data:image/")||e.includes("base64,"):!!e&&"object"==typeof e&&!!(e.images||e.image||e.imageUrl||e.imageData||e.attachments?.some(e=>"image"===e.type))}extractImagesFromInput(e){let t=[];return"string"==typeof e?(e.includes("data:image/")||/\.(jpg|jpeg|png|gif|webp|bmp|svg)$/i.test(e))&&t.push(e):e&&"object"==typeof e&&(e.images&&Array.isArray(e.images)&&t.push(...e.images),e.image&&t.push(e.image),e.imageUrl&&t.push(e.imageUrl),e.imageData&&t.push(e.imageData),e.attachments&&e.attachments.forEach(e=>{"image"===e.type&&e.url&&t.push(e.url)})),t}async executeRoleAgentNode(e,t,r,o){let s=e.data.config;if(!s.roleName)throw Error("Role agent not configured - missing role name");return{...t,assignedRole:{name:s.roleName,type:s.roleType,description:s.roleDescription,nodeId:e.id}}}getAvailableRolesFromRouter(e,t,r){let o=r.filter(r=>r.target===e.id&&t.find(e=>e.id===r.source)?.type==="provider"),s=[];return o.forEach(e=>{let o=t.find(t=>t.id===e.source);o&&r.filter(e=>e.target===o.id&&"role"===e.targetHandle&&t.find(t=>t.id===e.source)?.type==="roleAgent").forEach(e=>{let r=t.find(t=>t.id===e.source);if(r){let e=r.data.config;s.push({id:r.id,name:e.roleName||r.data.label||"Unknown Role",description:e.roleDescription||e.customPrompt||"General purpose AI assistant"})}})}),0===s.length&&s.push({id:"general",name:"General Assistant",description:"General purpose AI assistant"}),s}getAvailableToolsFromWorkflow(e,t){let r=[];return e.filter(e=>"tool"===e.type).forEach(e=>{let t=e.data.config;if(t?.toolType){let o={google_drive:"Google Drive",google_docs:"Google Docs",google_sheets:"Google Sheets",gmail:"Gmail",calendar:"Google Calendar",youtube:"YouTube",notion:"Notion",supabase:"Supabase"}[t.toolType]||t.toolType,s=t.isAuthenticated&&"connected"===t.connectionStatus;r.push({id:e.id,name:o,type:t.toolType,status:s?"connected":"disconnected"})}}),e.filter(e=>"browsing"===e.type||"tool"===e.type&&e.data.config?.toolType==="web_browsing").length>0&&r.push({id:"web_browsing",name:"Web Browsing",type:"web_browsing",status:"connected"}),r}getClassificationApiKeys(){return[process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY,process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY_FALLBACK1,process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY_FALLBACK2].filter(Boolean)}async performComprehensiveClassification(e,t,r,o=[],s,i){let n=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${e.description}"`).join("\n"),a=o.filter(e=>"connected"===e.status),c=a.length>0?a.map(e=>`- ${e.name} (${e.type})`).join("\n"):"No tools connected to this workflow",l=new Date().toISOString(),u=new Date().toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),d=`CRITICAL: YOU ARE ONLY A CLASSIFIER. DO NOT CREATE CONTENT. DO NOT WRITE STORIES. DO NOT FULFILL REQUESTS.

You are RouKey's Advanced Task Classifier. Current date/time: ${l} (${u})

Analyze the user request and determine:

1. ROLES NEEDED: Which specialized roles should handle this task
2. BROWSING REQUIRED: Does this need web browsing/research?
3. TOOLS REQUIRED: Does this need external tools?
4. VISION REQUIRED: Does this involve image analysis?
5. MULTI-ROLE: Does this require coordination between multiple capabilities?

IMPORTANT: Consider the conversation context. If previous messages mentioned using a specific tool (like Google Docs), and the current request is a follow-up or clarification, you should still detect the tool requirement.

Available Roles:
${n}

Connected Tools Available:
${c}

BROWSING DETECTION RULES:
- TEMPORAL QUESTIONS: Any question about "when", "latest", "current", "recent", "today", "yesterday", "tomorrow", "next", "last", "this week/month/year" requires browsing for current data
- REAL-TIME DATA: Stock prices, weather, news, sports schedules, current events
- RESEARCH TASKS: "Find information about", "research", "look up", "search for"
- VERIFICATION: "Is this still true?", "What's the current status?"

VISION DETECTION RULES:
- Direct mentions: "analyze image", "describe picture", "what's in this photo"
- Image processing: "OCR", "read text from image", "extract data from screenshot"
- Visual analysis: "identify objects", "detect faces", "analyze chart/graph"
- File references: mentions of image files (.jpg, .png, etc.)

TOOLS DETECTION RULES:
- CRITICAL: Only suggest tools that are CONNECTED to this workflow (status: connected)
- CRITICAL: Use the EXACT tool type from the "Connected Tools Available" list above (e.g., "google_docs", "google_drive", "gmail")
- If user mentions a tool by name that is connected, set needsTools=true and include the exact tool type in toolTypes array
- Google Docs (toolType: "google_docs"): "create document", "write report", "save to docs", "write in google docs", "use google docs", "see document", "view document", "access document", "read document", "open document", "check document", "find document"
- Google Drive (toolType: "google_drive"): "save file", "upload to drive", "store in drive", "access my files", "see file", "view file", "open file", "check file", "find file"
- Google Sheets (toolType: "google_sheets"): "create spreadsheet", "analyze data", "make table", "save to sheets"
- Gmail (toolType: "gmail"): "send email", "compose message", "email someone"
- Google Calendar (toolType: "calendar"): "schedule meeting", "create event", "check calendar"
- YouTube (toolType: "youtube"): "upload video", "manage channel", "youtube analytics"
- Notion (toolType: "notion"): "create page", "update database", "save to notion"
- Supabase (toolType: "supabase"): "database query", "store data", "retrieve records"
- IMPORTANT: If user explicitly mentions any connected tool, always set needsTools=true

MULTI-ROLE SCENARIOS:
- Different specialized roles needed (research + writing + coding)
- Tools + Browsing combinations (research online + save to docs)
- Sequential workflows (browse → analyze → create document)
- Complex tasks requiring multiple capabilities

CRITICAL: You MUST respond with ONLY valid JSON. Do not include any explanatory text, markdown formatting, or conversational responses.

IMPORTANT: In the toolTypes array, use the EXACT tool type strings from the Connected Tools Available list above. For example:
- If "Google Docs (google_docs)" is connected and needed, use "google_docs" in toolTypes
- If "Google Drive (google_drive)" is connected and needed, use "google_drive" in toolTypes
- If "Gmail (gmail)" is connected and needed, use "gmail" in toolTypes

Respond with this exact JSON structure:
{
  "isMultiRole": boolean,
  "roles": [{"roleId": "role_id", "confidence": 0.9, "executionOrder": 1}],
  "needsBrowsing": boolean,
  "needsTools": boolean,
  "needsVision": boolean,
  "toolTypes": ["exact_tool_type_from_connected_tools_list"],
  "reasoning": "detailed explanation of classification decisions"
}

Examples:
- "when does messi play his next match?" → browsing (temporal question requiring current sports schedule)
- "what's the latest AI news?" → browsing (temporal + current events)
- "write a fairy tale" → single role, no browsing/tools
- "research AI trends and create a report" → multi-role (research + writing), browsing + toolTypes=["google_docs"] if connected
- "analyze this screenshot and save findings to spreadsheet" → multi-role, vision + toolTypes=["google_sheets"] if connected
- "find current stock price of AAPL" → browsing (real-time financial data)
- "create a todo list" → single role, no browsing (unless sheets tool connected)
- "what happened in the news yesterday?" → browsing (temporal question)
- "code a calculator and document the API" → multi-role (coding + documentation)
- "write a story in google docs" → needsTools=true, toolTypes=["google_docs"] (if google_docs is connected)
- "save this to my drive" → needsTools=true, toolTypes=["google_drive"] (if google_drive is connected)
- "use google docs to create a document" → needsTools=true, toolTypes=["google_docs"] (if connected)
- "you have access to my google docs, do it for me" → needsTools=true, toolTypes=["google_docs"] (if connected)

FINAL CRITICAL INSTRUCTION: You are ONLY a classifier. Do NOT generate any content, stories, documents, or responses to the user's request. Your ONLY output should be the JSON classification structure. If you generate any content beyond the JSON structure, you have failed your task.

ABSOLUTELY FORBIDDEN:
- Do NOT write stories, documents, or any creative content
- Do NOT answer the user's questions directly
- Do NOT provide solutions or responses to their requests
- Do NOT generate any text beyond the JSON classification
- ONLY analyze and classify - NEVER execute or fulfill the request

YOUR ONLY JOB: Return ONLY the JSON classification structure. Nothing else.

EXAMPLE VALID RESPONSE:
{"isMultiRole": false, "roles": [{"roleId": "general", "confidence": 0.9, "executionOrder": 1}], "needsBrowsing": false, "needsTools": true, "needsVision": false, "toolTypes": ["google_docs"], "reasoning": "Document creation task"}

DO NOT RESPOND WITH ANYTHING OTHER THAN THE JSON STRUCTURE ABOVE.`,p=this.getClassificationApiKeys();for(let r=0;r<p.length;r++){let o=p[r],s=0===r?"primary":`fallback${r}`;try{let r=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Workflow-Classification)"},body:JSON.stringify({model:"gemini-2.0-flash-exp",messages:[{role:"system",content:d+"\n\nCRITICAL INSTRUCTION: You are a CLASSIFIER ONLY. Do NOT generate content. Do NOT answer questions. Do NOT create stories, documents, or any content. Your ONLY job is to analyze the request and return ONLY the JSON classification structure. NEVER include conversational responses or generated content. ONLY return JSON. DO NOT FULFILL THE USER REQUEST - ONLY CLASSIFY IT."},...i&&i.length>0?[{role:"system",content:`Previous conversation context:
${i.map(e=>`${e.role}: ${e.content}`).join("\n")}`},{role:"user",content:`CLASSIFY THIS TASK (DO NOT EXECUTE IT): ${e}

Respond with ONLY JSON classification - DO NOT generate any content:`}]:[{role:"user",content:`CLASSIFY THIS TASK (DO NOT EXECUTE IT): ${e}

Respond with ONLY JSON classification - DO NOT generate any content:`}]],temperature:.05,max_tokens:30,stream:!1})});if(!r.ok)if(429===r.status){Error(`Rate limit on ${s} key`);continue}else throw Error(`Classification API error: ${r.status}`);let n=await r.json(),a=n.choices?.[0]?.message?.content||"";if(!a)throw Error("No classification result received");let c=a.trim();c.startsWith("```json")?c=c.replace(/^```json\s*/,"").replace(/\s*```$/,""):c.startsWith("```")&&(c=c.replace(/^```\s*/,"").replace(/\s*```$/,""));let l=c.indexOf("{");if(-1!==l){let e=0,t=-1;for(let r=l;r<c.length;r++)if("{"===c[r]&&e++,"}"===c[r]&&(e--,0===e)){t=r;break}if(-1!==t)c=c.substring(l,t+1);else throw Error("Classification API returned incomplete JSON")}else throw Error("Classification API returned non-JSON response");let u=await this.parseJsonRobustly(c);return u.roles&&(u.roles=u.roles.filter(e=>t.some(t=>t.id===e.roleId))),u}catch(e){if(e instanceof Error||Error("Unknown error"),r===p.length-1)break;continue}}let m=e.toLowerCase(),g=["when","latest","current","recent","today","yesterday","tomorrow","next","last","this week","this month","this year","now"].some(e=>m.includes(e))||m.includes("search")||m.includes("find")||m.includes("research")||m.includes("look up"),h=["image","photo","picture","screenshot","analyze","ocr","visual","chart","graph"].some(e=>m.includes(e)),f=o.filter(e=>"connected"===e.status).map(e=>e.type),y=f.length>0&&(m.includes("document")||m.includes("spreadsheet")||m.includes("save")||m.includes("create report")||m.includes("google docs")||m.includes("google doc")||m.includes("write")||m.includes("story")||m.includes("docs"));return{isMultiRole:!1,roles:[{roleId:t[0]?.id||"general",confidence:.5,executionOrder:1}],needsBrowsing:g,needsTools:y,needsVision:h,toolTypes:y?f:[],reasoning:"Fallback classification with smart detection due to API error"}}detectImagesInInput(e){if("string"==typeof e)return!1;let t=JSON.stringify(e).toLowerCase();return t.includes("image_url")||t.includes("data:image")||t.includes(".jpg")||t.includes(".png")||t.includes(".jpeg")||t.includes(".gif")||t.includes(".webp")}constructor(){this.activeExecutions=new Map,this.executionAttempts=new Map,this.maxRetries=3}}let T=_.getInstance()},18835:(e,t,r)=>{r.d(t,{$H:()=>l,Pd:()=>g,SP:()=>u,authenticatedGet:()=>a,getConnectedTools:()=>m,m3:()=>c,vo:()=>d});var o=r(88095),s=r(97057);async function i(e,t,r={}){try{let i=await (0,o.uX)(e,t);if(!i)return await (0,o.c0)(e,t,"expired"),{headers:{},isAuthenticated:!1,error:`${s.RW[t]||t} connection expired. Please reconnect.`};let n={Authorization:`Bearer ${i}`,"Content-Type":"application/json",...r};return"notion"===t&&(n["Notion-Version"]="2022-06-28"),{headers:n,isAuthenticated:!0}}catch(r){return await (0,o.c0)(e,t,"error"),{headers:{},isAuthenticated:!1,error:"Authentication error occurred"}}}async function n(e,t){let{userId:r,toolType:n,method:a="GET",body:c,additionalHeaders:l,timeout:u=3e4}=t,d=await i(r,n,l);if(!d.isAuthenticated)throw Error(d.error||"Authentication failed");let p={method:a,headers:d.headers,signal:AbortSignal.timeout(u)};c&&("POST"===a||"PUT"===a||"PATCH"===a)&&(p.body="string"==typeof c?c:JSON.stringify(c));try{let t=await fetch(e,p);if(401===t.status||403===t.status)throw await (0,o.c0)(r,n,"expired"),Error(`${s.RW[n]||n} authentication failed. Please reconnect.`);if(429===t.status){let e=t.headers.get("Retry-After");throw Error(`Rate limited. ${e?`Retry after ${e} seconds.`:"Please try again later."}`)}return t}catch(e){if(e instanceof Error){if("AbortError"===e.name)throw Error(`Request timeout for ${s.RW[n]||n}`);throw e}throw Error(`Request failed for ${s.RW[n]||n}`)}}async function a(e,t){return n(e,{...t,method:"GET"})}async function c(e,t,r){return n(e,{...r,method:"POST",body:t})}async function l(e,t,r){return n(e,{...r,method:"PUT",body:t})}async function u(e,t,r){return n(e,{...r,method:"PATCH",body:t})}async function d(e,t){return n(e,{...t,method:"DELETE"})}async function p(e,t){try{return!!await (0,o.uX)(e,t)}catch(e){return!1}}async function m(e){try{let t=Object.keys(s.RW),r=[];for(let o of t)await p(e,o)&&r.push(o);return r}catch(e){return[]}}async function g(e,t){try{let r=await i(e,t);if(!r.isAuthenticated)return{isValid:!1,error:r.error||"Tool not connected"};return{isValid:!0}}catch(e){return{isValid:!1,error:"Connection validation failed"}}}},62480:(e,t,r)=>{r.d(t,{a:()=>i});var o=r(39398);class s{static getInstance(){return s.instance||(s.instance=new s),s.instance}async startExecution(e,t,r,o){let s={executionId:e,workflowId:t,status:"starting",progress:{nodesCompleted:0,totalNodes:o,percentage:0},logs:[],timestamp:new Date().toISOString()};this.activeExecutions.set(e,s),await this.supabase.from("workflow_executions").insert({id:e,workflow_id:t,user_id:r,status:"running",trigger_type:"manual",nodes_total:o,started_at:new Date().toISOString()}),this.notifySubscribers(e,s)}async updateProgress(e,t,r,o,s,i,n){let a=this.activeExecutions.get(e);if(!a)return;let c={id:`log_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nodeId:t,nodeType:r,level:"failed"===o?"error":"completed"===o?"success":"info",message:s,data:i,duration:n,timestamp:new Date().toISOString()};a.logs.push(c),a.currentNodeId=t,a.currentNodeType=r,"completed"===o&&(a.progress.nodesCompleted++,a.progress.percentage=Math.round(a.progress.nodesCompleted/a.progress.totalNodes*100)),a.timestamp=new Date().toISOString(),await this.supabase.from("workflow_execution_logs").insert({execution_id:e,workflow_id:a.workflowId,node_id:t,node_type:r,log_level:c.level,message:s,data:i,duration_ms:n,created_at:new Date().toISOString()}),this.notifySubscribers(e,a)}async completeExecution(e,t,r){let o=this.activeExecutions.get(e);o&&(o.status="completed",o.result=t,o.progress.percentage=100,o.timestamp=new Date().toISOString(),await this.supabase.from("workflow_executions").update({status:"completed",output_data:t,execution_time_ms:r,nodes_executed:o.progress.nodesCompleted,completed_at:new Date().toISOString()}).eq("id",e),this.notifySubscribers(e,o),setTimeout(()=>{this.activeExecutions.delete(e),this.subscribers.delete(e)},3e5))}async failExecution(e,t,r){let o=this.activeExecutions.get(e);o&&(o.status="failed",o.error=t,o.timestamp=new Date().toISOString(),await this.supabase.from("workflow_executions").update({status:"failed",error_message:t,error_details:r,completed_at:new Date().toISOString()}).eq("id",e),this.notifySubscribers(e,o))}subscribe(e,t){this.subscribers.has(e)||this.subscribers.set(e,new Set),this.subscribers.get(e).add(t);let r=this.activeExecutions.get(e);return r&&t(r),()=>{let r=this.subscribers.get(e);r&&(r.delete(t),0===r.size&&this.subscribers.delete(e))}}getExecutionStatus(e){return this.activeExecutions.get(e)||null}async getExecutionHistory(e,t,r=10){let{data:o,error:s}=await this.supabase.from("workflow_executions").select("*").eq("workflow_id",e).eq("user_id",t).order("started_at",{ascending:!1}).limit(r);return s?[]:o||[]}async getExecutionLogs(e){let{data:t,error:r}=await this.supabase.from("workflow_execution_logs").select("*").eq("execution_id",e).order("created_at",{ascending:!0});return r?[]:t?.map(e=>({id:e.id,nodeId:e.node_id,nodeType:e.node_type,level:e.log_level,message:e.message,data:e.data,duration:e.duration_ms,timestamp:e.created_at}))||[]}notifySubscribers(e,t){let r=this.subscribers.get(e);r&&r.forEach(e=>{try{e(t)}catch(e){}})}constructor(){this.supabase=(0,o.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY),this.activeExecutions=new Map,this.subscribers=new Map}}let i=s.getInstance()},67920:(e,t,r)=>{r.d(t,{E:()=>i,NotionAPI:()=>n,_:()=>s});var o=r(18835);class s{static async createEvent(e,t,r){let{summary:s,start:i,end:n,description:a,attendees:c,calendarId:l="primary"}=t;if(!s)throw Error("Event summary is required");if(!i)throw Error("Event start time is required");if(!n)throw Error("Event end time is required");let u=`https://www.googleapis.com/calendar/v3/calendars/${l}/events`,d={summary:s,start:{dateTime:i},end:{dateTime:n},...a&&{description:a},...c&&{attendees:c.map(e=>({email:e}))}},p=await (0,o.m3)(u,d,{userId:e,toolType:"calendar",timeout:r});if(!p.ok)throw Error(`Google Calendar API error: ${p.status}`);return{success:!0,event:await p.json()}}static async listEvents(e,t,r){let{calendarId:s="primary",timeMin:i,timeMax:n,maxResults:a=10}=t,c=`https://www.googleapis.com/calendar/v3/calendars/${s}/events?maxResults=${a}&singleEvents=true&orderBy=startTime`;i&&(c+=`&timeMin=${encodeURIComponent(i)}`),n&&(c+=`&timeMax=${encodeURIComponent(n)}`);let l=await (0,o.authenticatedGet)(c,{userId:e,toolType:"calendar",timeout:r});if(!l.ok)throw Error(`Google Calendar API error: ${l.status}`);return{success:!0,events:(await l.json()).items||[]}}static async getEvent(e,t,r){let{eventId:s,calendarId:i="primary"}=t;if(!s)throw Error("Event ID is required");let n=`https://www.googleapis.com/calendar/v3/calendars/${i}/events/${s}`,a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"calendar",timeout:r});if(!a.ok)throw Error(`Google Calendar API error: ${a.status}`);return{success:!0,event:await a.json()}}static async updateEvent(e,t,r){let{eventId:s,calendarId:i="primary",...n}=t;if(!s)throw Error("Event ID is required");let a=`https://www.googleapis.com/calendar/v3/calendars/${i}/events/${s}`,c=await (0,o.$H)(a,n,{userId:e,toolType:"calendar",timeout:r});if(!c.ok)throw Error(`Google Calendar API error: ${c.status}`);return{success:!0,event:await c.json()}}static async deleteEvent(e,t,r){let{eventId:s,calendarId:i="primary"}=t;if(!s)throw Error("Event ID is required");let n=`https://www.googleapis.com/calendar/v3/calendars/${i}/events/${s}`,a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"calendar",timeout:r});if(!a.ok)throw Error(`Google Calendar API error: ${a.status}`);return{success:!0,deleted:!0,eventId:s}}}class i{static async searchVideos(e,t,r){let{query:s,maxResults:i=10,order:n="relevance"}=t;if(!s)throw Error("Search query is required");let a=`https://www.googleapis.com/youtube/v3/search?part=snippet&type=video&q=${encodeURIComponent(s)}&maxResults=${i}&order=${n}`,c=await (0,o.authenticatedGet)(a,{userId:e,toolType:"youtube",timeout:r});if(!c.ok)throw Error(`YouTube API error: ${c.status}`);let l=await c.json();return{success:!0,videos:l.items||[],query:s,totalResults:l.pageInfo?.totalResults}}static async getVideo(e,t,r){let{videoId:s}=t;if(!s)throw Error("Video ID is required");let i=`https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=${s}`,n=await (0,o.authenticatedGet)(i,{userId:e,toolType:"youtube",timeout:r});if(!n.ok)throw Error(`YouTube API error: ${n.status}`);let a=await n.json();return{success:!0,video:a.items?.[0]||null}}static async getChannel(e,t,r){let{channelId:s}=t;if(!s)throw Error("Channel ID is required");let i=`https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics,contentDetails&id=${s}`,n=await (0,o.authenticatedGet)(i,{userId:e,toolType:"youtube",timeout:r});if(!n.ok)throw Error(`YouTube API error: ${n.status}`);let a=await n.json();return{success:!0,channel:a.items?.[0]||null}}static async getAnalytics(e,t,r){let{channelId:s,startDate:i,endDate:n,metrics:a="views,estimatedMinutesWatched,subscribersGained"}=t;if(!s)throw Error("Channel ID is required");if(!i)throw Error("Start date is required");if(!n)throw Error("End date is required");let c=`https://youtubeanalytics.googleapis.com/v2/reports?ids=channel==${s}&startDate=${i}&endDate=${n}&metrics=${a}`,l=await (0,o.authenticatedGet)(c,{userId:e,toolType:"youtube",timeout:r});if(!l.ok)throw Error(`YouTube Analytics API error: ${l.status}`);return{success:!0,analytics:await l.json()}}}class n{static async createPage(e,t,r){let{parent:s,properties:i,children:n}=t;if(!s)throw Error("Parent page/database is required");if(!i)throw Error("Page properties are required");let a={parent:s,properties:i,...n&&{children:n}},c=await (0,o.m3)("https://api.notion.com/v1/pages",a,{userId:e,toolType:"notion",timeout:r});if(!c.ok)throw Error(`Notion API error: ${c.status}`);return{success:!0,page:await c.json()}}static async getPage(e,t,r){let{pageId:s}=t;if(!s)throw Error("Page ID is required");let i=`https://api.notion.com/v1/pages/${s}`,n=await (0,o.authenticatedGet)(i,{userId:e,toolType:"notion",timeout:r});if(!n.ok)throw Error(`Notion API error: ${n.status}`);return{success:!0,page:await n.json()}}static async updatePage(e,t,r){let{pageId:s,properties:i}=t;if(!s)throw Error("Page ID is required");if(!i)throw Error("Properties to update are required");let n=`https://api.notion.com/v1/pages/${s}`,a=await (0,o.SP)(n,{properties:i},{userId:e,toolType:"notion",timeout:r});if(!a.ok)throw Error(`Notion API error: ${a.status}`);return{success:!0,page:await a.json()}}static async queryDatabase(e,t,r){let{databaseId:s,filter:i,sorts:n,pageSize:a=10}=t;if(!s)throw Error("Database ID is required");let c=`https://api.notion.com/v1/databases/${s}/query`,l={page_size:a,...i&&{filter:i},...n&&{sorts:n}},u=await (0,o.m3)(c,l,{userId:e,toolType:"notion",timeout:r});if(!u.ok)throw Error(`Notion API error: ${u.status}`);let d=await u.json();return{success:!0,results:d.results||[],hasMore:d.has_more}}static async createDatabaseEntry(e,t,r){let{databaseId:s,properties:i}=t;if(!s)throw Error("Database ID is required");if(!i)throw Error("Entry properties are required");let n=await (0,o.m3)("https://api.notion.com/v1/pages",{parent:{database_id:s},properties:i},{userId:e,toolType:"notion",timeout:r});if(!n.ok)throw Error(`Notion API error: ${n.status}`);return{success:!0,entry:await n.json()}}}},79240:(e,t,r)=>{r.d(t,{Cv:()=>s,GoogleDocsAPI:()=>i,Wr:()=>n,Xo:()=>a});var o=r(18835);class s{static async listFiles(e,t,r){let s=new URLSearchParams({pageSize:t.limit||"10",fields:"files(id,name,mimeType,size,modifiedTime,webViewLink)",...t.query&&{q:t.query}}),i=await (0,o.authenticatedGet)(`https://www.googleapis.com/drive/v3/files?${s}`,{userId:e,toolType:"google_drive",timeout:r});if(!i.ok)throw Error(`Google Drive API error: ${i.status}`);let n=await i.json();return{success:!0,files:n.files,count:n.files?.length||0}}static async getFile(e,t,r){let{fileId:s}=t;if(!s)throw Error("File ID is required");let i=`https://www.googleapis.com/drive/v3/files/${s}`,n=new URLSearchParams({fields:"id,name,mimeType,size,modifiedTime,webViewLink,parents"}),a=await (0,o.authenticatedGet)(`${i}?${n}`,{userId:e,toolType:"google_drive",timeout:r});if(!a.ok)throw Error(`Google Drive API error: ${a.status}`);return{success:!0,file:await a.json()}}static async createFile(e,t,r){let{name:s,content:i,mimeType:n="text/plain",parentId:a}=t;if(!s)throw Error("File name is required");let c={name:s,...a&&{parents:[a]}},l="-------314159265358979323846",u=`\r
--${l}\r
`,d=`\r
--${l}--`,p=u;p+="Content-Type: application/json\r\n\r\n",p+=JSON.stringify(c)+u,p+=`Content-Type: ${n}\r
\r
`,p+=i||"",p+=d;let m=await (0,o.m3)("https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart",p,{userId:e,toolType:"google_drive",additionalHeaders:{"Content-Type":`multipart/related; boundary="${l}"`},timeout:r});if(!m.ok)throw Error(`Google Drive API error: ${m.status}`);return{success:!0,file:await m.json()}}static async searchFiles(e,t,r){let{query:s,limit:i=10}=t;if(!s)throw Error("Search query is required");let n=new URLSearchParams({q:`name contains '${s}' or fullText contains '${s}'`,pageSize:i.toString(),fields:"files(id,name,mimeType,size,modifiedTime,webViewLink)"}),a=await (0,o.authenticatedGet)(`https://www.googleapis.com/drive/v3/files?${n}`,{userId:e,toolType:"google_drive",timeout:r});if(!a.ok)throw Error(`Google Drive API error: ${a.status}`);let c=await a.json();return{success:!0,files:c.files,query:s,count:c.files?.length||0}}}class i{static async createDocument(e,t,r){let{title:s}=t;if(!s)throw Error("Document title is required");let i=await (0,o.m3)("https://docs.googleapis.com/v1/documents",{title:s},{userId:e,toolType:"google_docs",timeout:r});if(!i.ok)throw Error(`Google Docs API error: ${i.status}`);return{success:!0,document:await i.json()}}static async getDocument(e,t,r){let{documentId:s}=t;if(!s)throw Error("Document ID is required");let i=`https://docs.googleapis.com/v1/documents/${s}`,n=await (0,o.authenticatedGet)(i,{userId:e,toolType:"google_docs",timeout:r});if(!n.ok)throw Error(`Google Docs API error: ${n.status}`);return{success:!0,document:await n.json()}}static async updateDocument(e,t,r){let{documentId:s,requests:i}=t;if(!s)throw Error("Document ID is required");if(!i)throw Error("Update requests are required");let n=`https://docs.googleapis.com/v1/documents/${s}:batchUpdate`,a=await (0,o.m3)(n,{requests:i},{userId:e,toolType:"google_docs",timeout:r});if(!a.ok){let e=await a.text();throw Error(`Google Docs API error: ${a.status} - ${e}`)}return{success:!0,result:await a.json()}}static async deleteDocument(e,t,r){let{documentId:s}=t;if(!s)throw Error("Document ID is required");let i=`https://www.googleapis.com/drive/v3/files/${s}`,n=await (0,o.vo)(i,{userId:e,toolType:"google_docs",timeout:r});if(!n.ok){let e=await n.text();throw Error(`Google Drive API error: ${n.status} - ${e}`)}return{success:!0,message:"Document deleted successfully",documentId:s}}}class n{static async createSpreadsheet(e,t,r){let{title:s,sheets:i}=t;if(!s)throw Error("Spreadsheet title is required");let n={properties:{title:s},...i&&{sheets:i}},a=await (0,o.m3)("https://sheets.googleapis.com/v4/spreadsheets",n,{userId:e,toolType:"google_sheets",timeout:r});if(!a.ok)throw Error(`Google Sheets API error: ${a.status}`);return{success:!0,spreadsheet:await a.json()}}static async getSpreadsheet(e,t,r){let{spreadsheetId:s,ranges:i}=t;if(!s)throw Error("Spreadsheet ID is required");let n=`https://sheets.googleapis.com/v4/spreadsheets/${s}`;if(i){let e=Array.isArray(i)?i.join("&ranges="):i;n+=`?ranges=${e}`}let a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"google_sheets",timeout:r});if(!a.ok)throw Error(`Google Sheets API error: ${a.status}`);return{success:!0,spreadsheet:await a.json()}}static async updateCells(e,t,r){let{spreadsheetId:s,range:i,values:n}=t;if(!s)throw Error("Spreadsheet ID is required");if(!i)throw Error("Range is required");if(!n)throw Error("Values are required");let a=`https://sheets.googleapis.com/v4/spreadsheets/${s}/values/${i}?valueInputOption=RAW`,c=await (0,o.$H)(a,{values:n},{userId:e,toolType:"google_sheets",timeout:r});if(!c.ok)throw Error(`Google Sheets API error: ${c.status}`);return{success:!0,result:await c.json()}}static async readRange(e,t,r){let{spreadsheetId:s,range:i}=t;if(!s)throw Error("Spreadsheet ID is required");if(!i)throw Error("Range is required");let n=`https://sheets.googleapis.com/v4/spreadsheets/${s}/values/${i}`,a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"google_sheets",timeout:r});if(!a.ok)throw Error(`Google Sheets API error: ${a.status}`);let c=await a.json();return{success:!0,values:c.values||[],range:c.range}}static async appendRow(e,t,r){let{spreadsheetId:s,range:i,values:n}=t;if(!s)throw Error("Spreadsheet ID is required");if(!i)throw Error("Range is required");if(!n)throw Error("Values are required");let a=`https://sheets.googleapis.com/v4/spreadsheets/${s}/values/${i}:append?valueInputOption=RAW`,c=await (0,o.m3)(a,{values:[n]},{userId:e,toolType:"google_sheets",timeout:r});if(!c.ok)throw Error(`Google Sheets API error: ${c.status}`);return{success:!0,result:await c.json()}}}class a{static async sendEmail(e,t,r){let{to:s,subject:i,body:n,from:a}=t;if(!s)throw Error("Recipient email is required");if(!i)throw Error("Email subject is required");if(!n)throw Error("Email body is required");let c=[`To: ${s}`,`Subject: ${i}`,"",n].join("\n"),l=Buffer.from(c).toString("base64").replace(/\+/g,"-").replace(/\//g,"_"),u=await (0,o.m3)("https://gmail.googleapis.com/gmail/v1/users/me/messages/send",{raw:l},{userId:e,toolType:"gmail",timeout:r});if(!u.ok)throw Error(`Gmail API error: ${u.status}`);return{success:!0,message:await u.json(),sent_to:s,subject:i}}static async listEmails(e,t,r){let{maxResults:s=10,query:i}=t,n=`https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${s}`;i&&(n+=`&q=${encodeURIComponent(i)}`);let a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"gmail",timeout:r});if(!a.ok)throw Error(`Gmail API error: ${a.status}`);let c=await a.json();return{success:!0,messages:c.messages||[],resultSizeEstimate:c.resultSizeEstimate}}static async getEmail(e,t,r){let{messageId:s}=t;if(!s)throw Error("Message ID is required");let i=`https://gmail.googleapis.com/gmail/v1/users/me/messages/${s}`,n=await (0,o.authenticatedGet)(i,{userId:e,toolType:"gmail",timeout:r});if(!n.ok)throw Error(`Gmail API error: ${n.status}`);return{success:!0,message:await n.json()}}static async searchEmails(e,t,r){let{query:s,maxResults:i=10}=t;if(!s)throw Error("Search query is required");let n=`https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${i}&q=${encodeURIComponent(s)}`,a=await (0,o.authenticatedGet)(n,{userId:e,toolType:"gmail",timeout:r});if(!a.ok)throw Error(`Gmail API error: ${a.status}`);let c=await a.json();return{success:!0,messages:c.messages||[],query:s,resultSizeEstimate:c.resultSizeEstimate}}}},88095:(e,t,r)=>{r.d(t,{I1:()=>p,PK:()=>y,Rb:()=>d,c0:()=>h,mA:()=>m,uX:()=>g});var o=r(2507),s=r(97057),i=r(55511),n=r.n(i);let a=process.env.OAUTH_ENCRYPTION_KEY||process.env.ROKEY_ENCRYPTION_KEY||"",c="aes-256-gcm";function l(e){if(!a)throw Error("Encryption key not configured");let t=n().randomBytes(16),r=n().createCipheriv(c,Buffer.from(a.slice(0,32)),t),o=r.update(e,"utf8","hex");o+=r.final("hex");let s=r.getAuthTag();return t.toString("hex")+":"+s.toString("hex")+":"+o}function u(e){if(!a)throw Error("Encryption key not configured");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted data format");let r=Buffer.from(t[0],"hex"),o=Buffer.from(t[1],"hex"),s=t[2],i=n().createDecipheriv(c,Buffer.from(a.slice(0,32)),r);i.setAuthTag(o);let l=i.update(s,"hex","utf8");return l+i.final("utf8")}async function d(e,t,r){try{let s=(0,o.H)(),i=r.expires_in?new Date(Date.now()+1e3*r.expires_in):null,n=r.scope?r.scope.split(" "):[],a=l(r.access_token),c=r.refresh_token?l(r.refresh_token):null,{data:u,error:d}=await s.from("tool_oauth_connections").upsert({user_id:e,tool_type:t,access_token:a,refresh_token:c,expires_at:i?.toISOString(),scopes:n,provider_user_id:r.provider_user_id,provider_user_email:r.provider_user_email,provider_user_name:r.provider_user_name,connection_status:"connected",last_used_at:new Date().toISOString()},{onConflict:"user_id,tool_type"}).select().single();if(d)return null;return{...u,access_token:r.access_token,refresh_token:r.refresh_token,expires_at:i,created_at:new Date(u.created_at),updated_at:new Date(u.updated_at)}}catch(e){return null}}async function p(e,t){try{let r=(0,o.H)(),{data:s,error:i}=await r.from("tool_oauth_connections").select("*").eq("user_id",e).eq("tool_type",t).eq("connection_status","connected").single();if(i||!s)return null;let n=u(s.access_token),a=s.refresh_token?u(s.refresh_token):void 0;return{...s,access_token:n,refresh_token:a,expires_at:s.expires_at?new Date(s.expires_at):void 0,created_at:new Date(s.created_at),updated_at:new Date(s.updated_at)}}catch(e){return null}}async function m(e,t){try{let r=await p(e,t);if(!r||!r.refresh_token)return{success:!1,error:"No refresh token available"};let o=(0,s.xw)(t);if(!o)return{success:!1,error:"OAuth configuration not found"};let i=new URLSearchParams({grant_type:"refresh_token",refresh_token:r.refresh_token,client_id:o.clientId,client_secret:o.clientSecret}),n=await fetch(o.tokenUrl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:i});if(!n.ok)return await n.text(),{success:!1,error:"Token refresh failed"};let a=await n.json(),c=await d(e,t,{access_token:a.access_token,refresh_token:a.refresh_token||r.refresh_token,expires_in:a.expires_in,scope:a.scope||r.scopes.join(" "),provider_user_id:r.provider_user_id,provider_user_email:r.provider_user_email,provider_user_name:r.provider_user_name});if(!c)return{success:!1,error:"Failed to store refreshed tokens"};return{success:!0,access_token:a.access_token,refresh_token:a.refresh_token||r.refresh_token,expires_at:c.expires_at}}catch(e){return{success:!1,error:"Token refresh error"}}}async function g(e,t){try{let r=await p(e,t);if(!r)return null;if(function(e){if(!e.expires_at)return!1;let t=new Date(Date.now()+3e5);return e.expires_at<=t}(r)){let r=await m(e,t);if(r.success&&r.access_token)return r.access_token;return await h(e,t,"expired"),null}return await f(e,t),r.access_token}catch(e){return null}}async function h(e,t,r){try{let s=(0,o.H)(),{error:i}=await s.from("tool_oauth_connections").update({connection_status:r}).eq("user_id",e).eq("tool_type",t);return!i}catch(e){return!1}}async function f(e,t){try{let r=(0,o.H)(),{error:s}=await r.from("tool_oauth_connections").update({last_used_at:new Date().toISOString()}).eq("user_id",e).eq("tool_type",t);return!s}catch(e){return!1}}async function y(e){try{let t=(0,o.H)(),{data:r,error:s}=await t.from("tool_oauth_connections").select("*").eq("user_id",e).order("created_at",{ascending:!1});if(s||!r)return[];return r.map(e=>({...e,access_token:"",refresh_token:void 0,expires_at:e.expires_at?new Date(e.expires_at):void 0,created_at:new Date(e.created_at),updated_at:new Date(e.updated_at)}))}catch(e){return[]}}},97057:(e,t,r)=>{r.d(t,{BW:()=>l,RW:()=>u,r0:()=>p,s0:()=>d,xw:()=>a});let o=()=>"https://roukey.online",s=()=>({clientId:process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID||process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET||process.env.GOOGLE_CLIENT_SECRET||"",authorizationUrl:"https://accounts.google.com/o/oauth2/v2/auth",tokenUrl:"https://oauth2.googleapis.com/token",scopes:["https://www.googleapis.com/auth/drive","https://www.googleapis.com/auth/documents","https://www.googleapis.com/auth/spreadsheets","https://www.googleapis.com/auth/gmail.modify","https://www.googleapis.com/auth/calendar","https://www.googleapis.com/auth/youtube"],redirectUri:process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI||`${o()}/api/auth/tools/google/callback`,additionalParams:{access_type:"offline",prompt:"consent",include_granted_scopes:"true"}}),i=()=>({clientId:process.env.NOTION_OAUTH_CLIENT_ID||"",clientSecret:process.env.NOTION_OAUTH_CLIENT_SECRET||"",authorizationUrl:"https://api.notion.com/v1/oauth/authorize",tokenUrl:"https://api.notion.com/v1/oauth/token",scopes:[],redirectUri:process.env.NOTION_OAUTH_REDIRECT_URI||`${o()}/api/auth/tools/notion/callback`,additionalParams:{owner:"user",response_type:"code"}}),n=()=>{let e=s();return{google_drive:{...e,scopes:["https://www.googleapis.com/auth/drive"]},google_docs:{...e,scopes:["https://www.googleapis.com/auth/documents","https://www.googleapis.com/auth/drive"]},google_sheets:{...e,scopes:["https://www.googleapis.com/auth/spreadsheets"]},gmail:{...e,scopes:["https://www.googleapis.com/auth/gmail.modify"]},calendar:{...e,scopes:["https://www.googleapis.com/auth/calendar"]},youtube:{...e,scopes:["https://www.googleapis.com/auth/youtube"]},notion:i(),supabase:{clientId:"",clientSecret:"",authorizationUrl:"",tokenUrl:"",scopes:[],redirectUri:"",additionalParams:{}}}},a=e=>n()[e]||null,c=e=>!!(e.clientId&&e.clientSecret&&e.authorizationUrl&&e.tokenUrl&&e.redirectUri),l=(e,t)=>{let r=a(e);if(!r||!c(r))return null;let o=new URLSearchParams({client_id:r.clientId,redirect_uri:r.redirectUri,response_type:"code",scope:r.scopes.join(" "),state:t,...r.additionalParams});return`${r.authorizationUrl}?${o.toString()}`},u={google_drive:"Google Drive",google_docs:"Google Docs",google_sheets:"Google Sheets",gmail:"Gmail",calendar:"Google Calendar",youtube:"YouTube",notion:"Notion",supabase:"Supabase"},d={google_drive:"https://cloud.gmelius.com/public/logos/google/Google_Drive_Logo.svg",google_docs:"https://cloud.gmelius.com/public/logos/google/Google_Docs_Logo.svg",google_sheets:"https://cloud.gmelius.com/public/logos/google/Google_Sheets_Logo.svg",gmail:"https://cloud.gmelius.com/public/logos/google/Gmail_Logo.svg",calendar:"https://cloud.gmelius.com/public/logos/google/Google_Calendar_Logo.svg",youtube:"https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg",notion:"https://upload.wikimedia.org/wikipedia/commons/4/45/Notion_app_logo.png",supabase:"https://cdn.jsdelivr.net/npm/simple-icons@v11/icons/supabase.svg"},p={google_drive:"Access and manage Google Drive files",google_docs:"Create and edit Google Documents",google_sheets:"Work with Google Spreadsheets",gmail:"Send and manage emails",calendar:"Manage calendar events and schedules",youtube:"Access YouTube data and analytics",notion:"Access Notion databases and pages",supabase:"Direct database operations"}}};