exports.id=9805,exports.ids=[9805],exports.modules={39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},62706:(e,t,s)=>{"use strict";s.d(t,{cr:()=>r});var a=s(39398);async function r(e){try{let t=(0,a.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{data:s,error:r}=await t.from("subscriptions").select("tier, status").eq("user_id",e).eq("status","active").single();if(r||!s)return"free";return({starter:"starter",professional:"professional"})[s.tier]||"free"}catch(e){return"free"}}},78335:()=>{},87846:(e,t,s)=>{"use strict";s.d(t,{R:()=>u});var a=s(88108),r=s(39398),i=s(55511),n=s.n(i);class o{get(e,t,s){let a=this.cache.get(e);return a?Date.now()-a.timestamp>this.TTL?(this.cache.delete(e),null):a.userId!==t||a.configId!==s?null:a.result:null}set(e,t,s,a){this.cache.size>=this.MAX_SIZE&&this.cleanup(),this.cache.set(e,{result:t,timestamp:Date.now(),userId:s,configId:a})}cleanup(){let e=Date.now(),t=[];for(let[s,a]of this.cache.entries())e-a.timestamp>this.TTL&&t.push(s);if(t.forEach(e=>this.cache.delete(e)),this.cache.size>=this.MAX_SIZE){let e=Array.from(this.cache.entries()).sort((e,t)=>e[1].timestamp-t[1].timestamp),t=Math.floor(.2*this.MAX_SIZE);for(let s=0;s<t;s++)this.cache.delete(e[s][0])}}getStats(){return{size:this.cache.size,maxSize:this.MAX_SIZE,ttl:this.TTL}}constructor(){this.cache=new Map,this.TTL=3e5,this.MAX_SIZE=1e3}}let c=new o;function h(){return(0,r.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}})}class l{constructor(){this.tierConfig={free:{enabled:!1,ttlHours:0,similarityThreshold:.9,maxCacheSize:0},starter:{enabled:!1,ttlHours:0,similarityThreshold:.9,maxCacheSize:0},professional:{enabled:!0,ttlHours:24,similarityThreshold:.85,maxCacheSize:1e3},enterprise:{enabled:!0,ttlHours:168,similarityThreshold:.8,maxCacheSize:1e4}}}static getInstance(){return l.instance||(l.instance=new l),l.instance}generatePromptHash(e,t,s){let a=`${e}|${t}|${s||0}`;return n().createHash("sha256").update(a).digest("hex")}isCacheEnabled(e){return this.tierConfig[e].enabled}getTierConfig(e){return this.tierConfig[e]}async searchCache(e,t,s,r){Date.now();try{if(!this.isCacheEnabled(r))return null;let i=this.getTierConfig(r),n=this.generatePromptHash(e.promptText,e.modelUsed,e.temperature),o=c.get(n,t,s);if(o)return Date.now(),o;let l=h(),{data:u,error:d}=await l.from("semantic_cache").select("*").eq("prompt_hash",n).eq("user_id",t).eq("custom_api_config_id",s).gt("expires_at",new Date().toISOString()).limit(1).single();if(u&&!d){Date.now();let e=this.formatCachedResult(u,1);return c.set(n,e,t,s),await this.incrementHitCount(u.id),e}Date.now();let p=await a.jinaEmbeddings.embedQuery(e.promptText);Date.now();let{data:m,error:_}=await l.rpc("search_semantic_cache",{query_embedding:p,config_id:s,user_id_param:t,similarity_threshold:i.similarityThreshold,match_count:1});if(_)return null;if(m&&m.length>0){let e=m[0];Date.now();let a=this.formatCachedResult(e,e.similarity);return c.set(n,a,t,s),await this.incrementHitCount(e.id),a}return Date.now(),null}catch(e){return Date.now(),null}}async storeCache(e,t,s,r,i){try{if(!this.isCacheEnabled(i))return!1;let n=this.getTierConfig(i),o=h(),c=await a.jinaEmbeddings.embedQuery(e.promptText),l=this.generatePromptHash(e.promptText,e.modelUsed,e.temperature),u=new Date;u.setHours(u.getHours()+n.ttlHours);let{error:d}=await o.from("semantic_cache").insert({user_id:s,custom_api_config_id:r,prompt_text:e.promptText,prompt_embedding:c,prompt_hash:l,model_used:e.modelUsed,provider_used:e.providerUsed,temperature:e.temperature,max_tokens:e.maxTokens,request_metadata:e.metadata||{},response_data:t.responseData,response_tokens_prompt:t.tokensPrompt,response_tokens_completion:t.tokensCompletion,response_cost:t.cost,cache_tier:i,expires_at:u.toISOString()});if(d)return!1;return!0}catch(e){return!1}}async getCacheStats(e,t,s=7){try{let a=h(),r=new Date;r.setDate(r.getDate()-s);let{data:i,error:n}=await a.from("semantic_cache_analytics").select("*").eq("user_id",e).eq("custom_api_config_id",t).gte("date",r.toISOString().split("T")[0]);if(n||!i||0===i.length)return this.getEmptyStats();let o=i.reduce((e,t)=>({totalRequests:e.totalRequests+(t.total_requests||0),cacheHits:e.cacheHits+(t.cache_hits||0),cacheMisses:e.cacheMisses+(t.cache_misses||0),tokensSaved:e.tokensSaved+(t.tokens_saved||0),costSaved:e.costSaved+(t.cost_saved||0),responseTimeSum:e.responseTimeSum+(t.avg_response_time_ms||0),rowCount:e.rowCount+1}),{totalRequests:0,cacheHits:0,cacheMisses:0,tokensSaved:0,costSaved:0,responseTimeSum:0,rowCount:0});return{totalRequests:o.totalRequests,cacheHits:o.cacheHits,cacheMisses:o.cacheMisses,hitRate:o.totalRequests>0?o.cacheHits/o.totalRequests:0,tokensSaved:o.tokensSaved,costSaved:o.costSaved,avgResponseTime:o.rowCount>0?o.responseTimeSum/o.rowCount:0}}catch(e){return this.getEmptyStats()}}async cleanupExpiredCache(){try{let e=h(),{data:t,error:s}=await e.rpc("cleanup_semantic_cache");if(s)return 0;return t||0}catch(e){return 0}}async incrementHitCount(e){try{let t=h();await t.rpc("increment_cache_hit",{cache_id:e})}catch(e){}}formatCachedResult(e,t){return{id:e.id,promptText:e.prompt_text,responseData:e.response_data,modelUsed:e.model_used,providerUsed:e.provider_used,similarity:t,hitCount:e.hit_count,createdAt:e.created_at,expiresAt:e.expires_at}}getEmptyStats(){return{totalRequests:0,cacheHits:0,cacheMisses:0,hitRate:0,tokensSaved:0,costSaved:0,avgResponseTime:0}}}let u=l.getInstance()},88108:(e,t,s)=>{"use strict";s.d(t,{jinaEmbeddings:()=>r});class a{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,s=!1){let a=this.keyUsage.get(e);a&&(a.requests++,a.tokens+=t,a.lastUsed=new Date,s&&(a.errors++,a.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,s=null;for(let a=0;a<t;a++)try{let t=this.getBestKey(),s=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!s.ok){let e=await s.text();if(429===s.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${s.status}: ${e}`)}let a=await s.json();if(!a.data||0===a.data.length)throw Error("No embedding data returned from Jina API");let r=a.data[0].embedding;return this.updateKeyUsage(t,a.usage?.total_tokens||e.length),r}catch(e){if(s=e,a===t-1)break}throw Error(`All Jina API keys failed. Last error: ${s?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let s=0;s<e.length;s++){let a=await this.embedQuery(e[s]);t.push(a),s<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,s)=>{let a=this.keyUsage.get(t);a&&(e[`key_${s+1}`]={...a})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let r=new a},96487:()=>{}};