(()=>{var e={};e.id=7637,e.ids=[7637],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10258:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413);function a(){return(0,s.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3"}),(0,s.jsx)("div",{className:"h-6 bg-gray-200 rounded w-2/3"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 space-y-6",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/3"})]})]})]})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20404:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(){let[e,t]=(0,s.useState)({isOpen:!1,isLoading:!1,title:"",message:"",confirmText:"Confirm",cancelText:"Cancel",type:"danger",onConfirm:()=>{}}),r=(0,s.useCallback)((e,r)=>{t({isOpen:!0,isLoading:!1,title:e.title,message:e.message,confirmText:e.confirmText||"Confirm",cancelText:e.cancelText||"Cancel",type:e.type||"danger",onConfirm:async()=>{t(e=>({...e,isLoading:!0}));try{await r(),t(e=>({...e,isOpen:!1,isLoading:!1}))}catch(e){throw t(e=>({...e,isLoading:!1})),e}}})},[]),a=(0,s.useCallback)(()=>{t(e=>({...e,isOpen:!1,isLoading:!1}))},[]);return{...e,showConfirmation:r,hideConfirmation:a}}},26403:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38149:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["training",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79729)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(r.bind(r,10258)),"C:\\RoKey App\\rokey-app\\src\\app\\training\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\RoKey App\\rokey-app\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/training/page",pathname:"/training",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(43210),n=r(62688);let i=(0,n.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),o=(0,n.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),l=(0,n.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),d=(0,n.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),c=(0,n.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),u=(0,n.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var m=r(11860),p=r(20404),x=r(50181);function h({configId:e,onDocumentUploaded:t,onDocumentDeleted:r,theme:n="light"}){let[h,g]=(0,a.useState)([]),[f,y]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),[w,j]=(0,a.useState)(0),[k,N]=(0,a.useState)(!1),[C,T]=(0,a.useState)(null),[A,$]=(0,a.useState)(null),_=(0,a.useRef)(null),P=(0,p.Z)(),M=(0,a.useCallback)(async(t=0,r)=>{if(e){0===t&&v(!0);try{let s=await fetch(`/api/documents/list?configId=${e}`);if(s.ok){let e=(await s.json()).documents||[];if(r&&t<3&&!e.find(e=>e.id===r))return void setTimeout(()=>{M(t+1,r)},(t+1)*500);g(t=>{if(!r||e.find(e=>e.id===r))return e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime());{let r=new Set(e.map(e=>e.id)),s=t.filter(e=>!r.has(e.id));return[...e,...s].sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}})}}catch(e){t<2&&setTimeout(()=>{M(t+1,r)},1e3)}finally{(0===t||r)&&v(!1)}}},[e]),S=async r=>{if(!e)return void T("Please select an API configuration first");let s=r[0];if(s){if(!["application/pdf","text/plain","text/markdown"].includes(s.type))return void T("Please upload PDF, TXT, or MD files only");if(s.size>0xa00000)return void T("File size must be less than 10MB");y(!0),T(null),$(null),j(0);try{let r=new FormData;r.append("file",s),r.append("configId",e);let a=setInterval(()=>{j(e=>Math.min(e+10,90))},200),n=await fetch("/api/documents/upload",{method:"POST",body:r});if(clearInterval(a),j(100),!n.ok){let e=await n.json();throw Error(e.error||"Upload failed")}let i=await n.json();$(`✨ ${s.name} uploaded successfully! Processing ${i.document.chunks_total} chunks.`);let o={id:i.document.id,filename:i.document.filename||s.name,file_type:s.type,file_size:s.size,status:i.document.status||"processing",chunks_count:i.document.chunks_processed||0,created_at:new Date().toISOString()};g(e=>e.find(e=>e.id===o.id)?e.map(e=>e.id===o.id?o:e):[o,...e]),setTimeout(async()=>{await M(0,i.document.id)},200),t?.()}catch(e){T(`Upload failed: ${e.message}`),setTimeout(()=>T(null),8e3)}finally{y(!1),j(0),_.current&&(_.current.value=""),A&&setTimeout(()=>$(null),5e3)}}},D=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?N(!0):"dragleave"===e.type&&N(!1)},[]),L=(0,a.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),N(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&S(e.dataTransfer.files)},[e]),E=(e,t)=>{P.showConfirmation({title:"Delete Document",message:`Are you sure you want to delete "${t}"? This will permanently remove the document and all its processed chunks from your knowledge base. This action cannot be undone.`,confirmText:"Delete Document",cancelText:"Cancel",type:"danger"},async()=>{g(t=>t.filter(t=>t.id!==e));try{let t=await fetch(`/api/documents/${e}`,{method:"DELETE"});if(!t.ok){let e=await t.json().catch(()=>({error:"Unknown error"}));throw g(h),Error(e.error||`HTTP ${t.status}: Failed to delete document`)}await t.json(),$("Document deleted successfully"),setTimeout(async()=>{await M()},200),r?.(),setTimeout(()=>$(null),3e3)}catch(e){g(h),T(`Delete failed: ${e.message}`),setTimeout(()=>T(null),8e3)}})},B=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},I=e=>e.includes("pdf")?(0,s.jsx)(i,{className:"w-5 h-5 text-red-500"}):e.includes("word")?(0,s.jsx)(i,{className:"w-5 h-5 text-blue-500"}):(0,s.jsx)(o,{className:"w-5 h-5 text-gray-500"});return(0,s.jsxs)("div",{className:"space-y-6",children:[C&&(0,s.jsx)("div",{className:`rounded-xl p-4 ${"dark"===n?"bg-red-900/20 border border-red-500/30":"bg-red-50 border border-red-200"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l,{className:`w-5 h-5 ${"dark"===n?"text-red-400":"text-red-600"}`}),(0,s.jsx)("p",{className:`text-sm font-medium ${"dark"===n?"text-red-200":"text-red-800"}`,children:C})]})}),A&&(0,s.jsx)("div",{className:`rounded-xl p-4 ${"dark"===n?"bg-green-900/20 border border-green-500/30":"bg-green-50 border border-green-200"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d,{className:`w-5 h-5 ${"dark"===n?"text-green-400":"text-green-600"}`}),(0,s.jsx)("p",{className:`text-sm font-medium ${"dark"===n?"text-green-200":"text-green-800"}`,children:A})]})}),(0,s.jsxs)("div",{className:`relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-300 transform ${k?"dark"===n?"border-orange-400 bg-orange-900/20 scale-105 shadow-lg":"border-orange-400 bg-orange-50 scale-105 shadow-lg":"dark"===n?"border-gray-600 hover:border-orange-400 hover:bg-orange-900/10 hover:scale-102 hover:shadow-md":"border-gray-300 hover:border-orange-400 hover:bg-orange-50 hover:scale-102 hover:shadow-md"} ${!e?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,onDragEnter:D,onDragLeave:D,onDragOver:D,onDrop:L,onClick:()=>e&&_.current?.click(),children:[(0,s.jsx)("input",{ref:_,type:"file",className:"hidden",accept:".pdf,.txt,.md",onChange:e=>{e.target.files&&e.target.files[0]&&S(e.target.files)},disabled:!e||f}),f?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c,{className:"w-12 h-12 text-orange-500 mx-auto animate-spin"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:`text-lg font-medium ${"dark"===n?"text-white":"text-gray-900"}`,children:"Processing Document..."}),(0,s.jsx)("div",{className:`w-full rounded-full h-2 ${"dark"===n?"bg-gray-700":"bg-gray-200"}`,children:(0,s.jsx)("div",{className:"bg-orange-500 h-2 rounded-full transition-all duration-300",style:{width:`${w}%`}})}),(0,s.jsxs)("p",{className:`text-sm ${"dark"===n?"text-gray-300":"text-gray-600"}`,children:[w,"% complete"]})]})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(u,{className:`w-12 h-12 mx-auto ${"dark"===n?"text-gray-500":"text-gray-400"}`}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:`text-lg font-medium ${"dark"===n?"text-white":"text-gray-900"}`,children:e?"Upload Knowledge Documents":"Select a configuration first"}),(0,s.jsx)("p",{className:`text-sm mt-1 ${"dark"===n?"text-gray-300":"text-gray-600"}`,children:"Drag and drop files here, or click to browse"}),(0,s.jsx)("p",{className:`text-xs mt-2 ${"dark"===n?"text-gray-400":"text-gray-500"}`,children:"Supports PDF, TXT, MD files up to 10MB"})]})]})]}),h.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:`text-lg font-semibold ${"dark"===n?"text-white":"text-gray-900"}`,children:"Uploaded Documents"}),b&&(0,s.jsxs)("div",{className:`flex items-center space-x-2 text-sm ${"dark"===n?"text-gray-300":"text-gray-600"}`,children:[(0,s.jsx)(c,{className:"w-4 h-4 animate-spin"}),(0,s.jsx)("span",{children:"Refreshing..."})]})]}),(0,s.jsx)("div",{className:"grid gap-4",children:h.map(e=>(0,s.jsxs)("div",{className:`flex items-center justify-between p-4 rounded-xl transition-shadow ${"dark"===n?"bg-gray-800/50 border border-gray-700/50 hover:shadow-lg hover:shadow-gray-900/20":"bg-white border border-gray-200 hover:shadow-md"}`,children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[I(e.file_type),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:`font-medium ${"dark"===n?"text-white":"text-gray-900"}`,children:e.filename}),(0,s.jsxs)("p",{className:`text-sm ${"dark"===n?"text-gray-300":"text-gray-600"}`,children:[B(e.file_size)," • ",e.chunks_count," chunks"]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["completed"===e.status&&(0,s.jsx)(d,{className:"w-5 h-5 text-green-500"}),"processing"===e.status&&(0,s.jsx)(c,{className:"w-5 h-5 text-orange-500 animate-spin"}),"failed"===e.status&&(0,s.jsx)(l,{className:"w-5 h-5 text-red-500"}),(0,s.jsx)("span",{className:`text-sm font-medium ${"completed"===e.status?"text-green-600":"processing"===e.status?"text-orange-600":"text-red-600"}`,children:"completed"===e.status?"Ready":"processing"===e.status?"Processing":"Failed"})]}),(0,s.jsx)("button",{onClick:()=>E(e.id,e.filename),className:`p-1 transition-colors ${"dark"===n?"text-gray-500 hover:text-red-400":"text-gray-400 hover:text-red-500"}`,title:"Delete document",children:(0,s.jsx)(m.A,{className:"w-4 h-4"})})]})]},e.id))})]}),(0,s.jsx)(x.A,{isOpen:P.isOpen,onClose:P.hideConfirmation,onConfirm:P.onConfirm,title:P.title,message:P.message,confirmText:P.confirmText,cancelText:P.cancelText,type:P.type,isLoading:P.isLoading})]})}var g=r(4847),f=r(11016);function y(){let{subscriptionStatus:e}=(0,f.R)(),t=(0,p.Z)(),[r,n]=(0,a.useState)([]),[i,o]=(0,a.useState)(""),[l,d]=(0,a.useState)([]),[c,u]=(0,a.useState)(!1),[m,y]=(0,a.useState)(null),[b,v]=(0,a.useState)(null),[w,j]=(0,a.useState)(0),[k,N]=(0,a.useState)(""),C=async(e,t=0)=>{try{let r=Date.now(),s=await fetch(`/api/documents/list?configId=${e}&_t=${r}`,{cache:"no-store"});if(s.ok){let e=await s.json(),t=e.documents?.length||0;j(t)}else t<1&&setTimeout(()=>C(e,t+1),1e3)}catch(r){t<1&&setTimeout(()=>C(e,t+1),1e3)}},T=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let r of e.split("\n").filter(e=>e.trim())){let e=r.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let r=e.includes("→")?"→":"->",s=e.split(r);if(s.length>=2){let e=s[0].trim(),a=s.slice(1).join(r).trim();t.examples.push({input:e,output:a})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},A=async()=>{if(!i||!k.trim())return void y("Please select an API configuration and provide training prompts.");if(!c){u(!0),y(null),v(null);try{let e=T(k),t=r.find(e=>e.id===i)?.name||"Unknown Config",s={custom_api_config_id:i,name:`${t} Training - ${new Date().toLocaleDateString()}`,description:`Training job for ${t} with ${e.examples.length} examples`,training_data:{processed_prompts:e,raw_prompts:k.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},a=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){let e=await a.text();throw Error(`Failed to save training job: ${a.status} ${e}`)}let n=await a.json(),o="updated"===n.operation,l=`${o?"\uD83D\uDD04":"\uD83C\uDF89"} Prompt Engineering ${o?"updated":"completed"} successfully!

Your "${t}" configuration has been ${o?"updated":"enhanced"} with:
• ${e.examples.length} training examples
• Custom system instructions and behavior guidelines

✨ All future chats using this configuration will automatically:
• Follow your training examples
• Apply your behavior guidelines
• Maintain consistent personality and responses

🚀 Try it now in the Playground to see your ${o?"updated":"enhanced"} model in action!

💡 Your training prompts remain here so you can modify them anytime.`;v(l)}catch(e){y(`Failed to create prompt engineering: ${e.message}`)}finally{u(!1)}}};return(0,s.jsxs)("div",{className:"min-h-screen w-full bg-[#040716] text-white overflow-x-hidden",children:[(0,s.jsx)("div",{className:"border-b border-gray-800/50",children:(0,s.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 py-6",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"flex items-center space-x-8",children:(0,s.jsx)("h1",{className:"text-2xl font-semibold text-white",children:"Training"})}),(0,s.jsx)("div",{className:"flex items-center space-x-4",children:e&&(0,s.jsx)(g.yA,{tier:e.tier,size:"lg",theme:"dark"})})]})})}),(0,s.jsx)("div",{className:"w-full px-4 sm:px-6 lg:px-8 pb-8",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[m&&(0,s.jsx)("div",{className:"mb-6 bg-red-900/20 border border-red-500/30 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-red-200 text-sm font-medium",children:m})]})}),b&&(0,s.jsx)("div",{className:"mb-6 bg-green-900/20 border border-green-500/30 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("p",{className:"text-green-200 text-sm font-medium",children:b})]})}),(0,s.jsx)(g.sU,{feature:"knowledge_base",customMessage:"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.",theme:"dark",children:(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 mb-8 border border-gray-800/50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-orange-500/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Knowledge Documents"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Upload documents to enhance your AI with proprietary knowledge"})]})]}),e&&i&&(0,s.jsx)("div",{className:"mb-4 bg-gray-800/50 border border-gray-700/50 rounded-lg px-4 py-2",children:(0,s.jsx)(g.Jg,{current:w,limit:5*("professional"===e.tier),label:"Knowledge Base Documents",tier:e.tier,showUpgradeHint:!0,theme:"dark"})}),(0,s.jsx)(h,{configId:i,theme:"dark",onDocumentUploaded:()=>{i&&setTimeout(()=>{C(i)},500)},onDocumentDeleted:()=>{i&&setTimeout(()=>{C(i)},500)}})]})}),(0,s.jsx)(g.sU,{feature:"prompt_engineering",customMessage:"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.",theme:"dark",children:(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-sm rounded-lg p-8 border border-gray-800/50",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Custom Prompts"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Define behavior, examples, and instructions for your AI"})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-300 mb-2",children:"Select API Configuration"}),(0,s.jsxs)("select",{id:"configSelect",value:i,onChange:e=>o(e.target.value),className:"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm",children:[(0,s.jsx)("option",{value:"",children:"Choose which model to train..."}),r.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Prompts & Instructions"}),(0,s.jsx)("textarea",{id:"trainingPrompts",value:k,onChange:e=>N(e.target.value),placeholder:`Enter your training prompts using these formats:

SYSTEM: You are a helpful customer service agent for our company
BEHAVIOR: Always be polite and offer solutions

User asks about returns → I'd be happy to help with your return! Let me check our policy for you.
Customer is frustrated → I understand your frustration. Let me see how I can resolve this for you.

General instructions can be written as regular text.`,rows:12,className:"w-full px-4 py-3 border border-gray-700 rounded-xl focus:ring-2 focus:ring-cyan-500 focus:border-cyan-500 transition-colors bg-gray-800 text-white text-sm resize-none font-mono placeholder-gray-500"}),(0,s.jsxs)("div",{className:"mt-3 bg-blue-900/20 border border-blue-500/30 rounded-lg p-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-300 mb-2",children:"Training Format Guide:"}),(0,s.jsxs)("ul",{className:"text-xs text-blue-200 space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-700/50",children:[(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)("button",{type:"button",className:"btn-secondary-dark",onClick:()=>{t.showConfirmation({title:"Clear Training Prompts",message:"Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.",confirmText:"Clear All",cancelText:"Cancel",type:"warning"},()=>{N("")})},children:"Clear Form"})}),(0,s.jsx)("button",{type:"button",onClick:A,disabled:!i||!k.trim()||c,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})}),(0,s.jsx)(x.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading})]})})]})}},50181:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687);r(43210);var a=r(26403),n=r(59168),i=r(81836);function o({isOpen:e,onClose:t,onConfirm:r,title:o,message:l,confirmText:d="Delete",cancelText:c="Cancel",type:u="danger",isLoading:m=!1}){let p=(()=>{switch(u){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:a.A};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:n.A};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:n.A}}})(),x=p.icon;return e?(0,s.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:m?void 0:t}),(0,s.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,s.jsx)("div",{className:"relative px-6 pt-6",children:(0,s.jsx)("button",{onClick:t,disabled:m,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,s.jsx)(i.A,{className:"h-5 w-5"})})}),(0,s.jsxs)("div",{className:"px-6 pb-6",children:[(0,s.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,s.jsx)("div",{className:`${p.iconBg} rounded-full p-3`,children:(0,s.jsx)(x,{className:`h-8 w-8 ${p.iconColor}`})})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:o}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:l}),(0,s.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,s.jsx)("button",{type:"button",onClick:t,disabled:m,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:c}),(0,s.jsx)("button",{type:"button",onClick:r,disabled:m,className:`w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ${p.confirmButton}`,children:m?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},52587:(e,t,r)=>{Promise.resolve().then(r.bind(r,40889))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62688:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:i,iconNode:c,...u},m)=>(0,s.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",n),...!i&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),u=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...n},l)=>(0,s.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${a(i(e))}`,`lucide-${e}`,r),...n}));return r.displayName=i(e),r}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70149:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(43210);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18"}))})},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79729:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\training\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\training\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92339:(e,t,r)=>{Promise.resolve().then(r.bind(r,79729))},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,5449,2535,4912,4847],()=>r(38149));module.exports=s})();