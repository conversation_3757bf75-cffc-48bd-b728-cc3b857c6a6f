'use client';

import Image from 'next/image';
import { WrenchScrewdriverIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import BaseNode from './BaseNode';
import { WorkflowNode, ToolNodeData } from '@/types/manualBuild';
import { TOOL_DISPLAY_NAMES, TOOL_ICONS } from '@/lib/oauth/config';




interface ToolNodeProps {
  data: WorkflowNode['data'];
  onUpdate?: (nodeId: string, newData: any) => void;
}

export default function ToolNode({ data, onUpdate }: ToolNodeProps) {
  const config = data.config as ToolNodeData['config'];
  const toolType = config?.toolType;
  const toolIcon = toolType ? TOOL_ICONS[toolType] : '🔧';
  const toolName = toolType ? TOOL_DISPLAY_NAMES[toolType] : 'External Tool';
  const connectionStatus = config?.connectionStatus || 'disconnected';
  const isAuthenticated = config?.isAuthenticated || false;

  const getStatusColor = () => {
    if (isAuthenticated && connectionStatus === 'connected') {
      return 'text-green-400';
    }
    switch (connectionStatus) {
      case 'expired':
      case 'revoked':
      case 'error':
        return 'text-red-400';
      default:
        return 'text-yellow-400';
    }
  };

  const getStatusText = () => {
    if (isAuthenticated && connectionStatus === 'connected') {
      return 'Connected';
    }
    switch (connectionStatus) {
      case 'expired': return 'Expired';
      case 'revoked': return 'Revoked';
      case 'error': return 'Error';
      default: return 'Not Connected';
    }
  };

  const getStatusIcon = () => {
    if (isAuthenticated && connectionStatus === 'connected') {
      return <CheckCircleIcon className="h-4 w-4 text-green-400" />;
    }
    return <ExclamationTriangleIcon className="h-4 w-4 text-yellow-400" />;
  };

  const handleConfigUpdate = (newConfig: any) => {
    if (onUpdate && data.id && typeof data.id === 'string') {
      onUpdate(data.id, {
        ...data,
        config: newConfig
      });
    }
  };

  return (
    <>
      <BaseNode
        data={data}
        icon={WrenchScrewdriverIcon}
        color="#06b6d4"
        hasInput={false}
        hasOutput={true}
      >
        <div className="space-y-3">
          {toolType ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {toolIcon ? (
                    <Image
                      src={toolIcon}
                      alt={toolName}
                      width={20}
                      height={20}
                      className="rounded-sm"
                    />
                  ) : (
                    <span className="text-lg">🔧</span>
                  )}
                  <span className="text-sm font-medium text-white">
                    {toolName}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  {getStatusIcon()}
                </div>
              </div>

              <div className={`text-xs ${getStatusColor()} flex items-center gap-1`}>
                {getStatusText()}
                {config?.providerUserEmail && (
                  <span className="text-gray-400">
                    • {config.providerUserEmail}
                  </span>
                )}
              </div>

              {config?.timeout && (
                <div className="text-xs text-gray-400">
                  Timeout: {config.timeout}s
                </div>
              )}

              <div className={`text-xs px-2 py-1 rounded ${
                isAuthenticated && connectionStatus === 'connected'
                  ? 'text-green-300 bg-green-900/20'
                  : 'text-yellow-300 bg-yellow-900/20'
              }`}>
                {isAuthenticated && connectionStatus === 'connected'
                  ? '✓ Tool connected'
                  : '⚠️ Needs connection'
                }
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="text-sm text-gray-300">
                External Tool Integration
              </div>
              <div className="text-xs text-gray-400">
                Connect to external services like Google Drive, Notion, Gmail, and more.
              </div>
              <div className="text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded">
                ⚠️ Needs configuration
              </div>
            </div>
          )}
        </div>
      </BaseNode>


    </>
  );
}
