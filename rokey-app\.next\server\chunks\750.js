"use strict";exports.id=750,exports.ids=[750],exports.modules={750:(e,t,s)=>{s.d(t,{A:()=>o});class r{constructor(){this.apiKeys=[],this.currentKeyIndex=0,this.keyUsageCount=new Map,this.keyErrors=new Map,this.MAX_RETRIES=3,this.ERROR_THRESHOLD=5,this.ENDPOINT="https://production-sfo.browserless.io",this.initializeKeys(),this.testConnectivity()}static getInstance(){return r.instance||(r.instance=new r),r.instance}initializeKeys(){this.apiKeys=[process.env.BROWSERLESS_API_KEY,process.env.BROWSERLESS_API_KEY_2,process.env.BROWSERLESS_API_KEY_3,process.env.BROWSERLESS_API_KEY_4,process.env.BROWSERLESS_API_KEY_5,process.env.BROWSERLESS_API_KEY_6,process.env.BROWSERLESS_API_KEY_7,process.env.BROWSERLESS_API_KEY_8,process.env.BROWSERLESS_API_KEY_9,process.env.BROWSERLESS_API_KEY_10].filter(Boolean),0!==this.apiKeys.length&&this.apiKeys.forEach(e=>{this.keyUsageCount.set(e,0),this.keyErrors.set(e,0)})}async testConnectivity(){if(0!==this.apiKeys.length)try{let e=this.apiKeys[0],t=`${this.ENDPOINT}/function?token=${e}`,s=`
        export default async function ({ page }) {
          return {
            status: "connectivity-test-success",
            timestamp: new Date().toISOString()
          };
        }
      `,r=await fetch(t,{method:"POST",headers:{"Content-Type":"application/javascript"},body:s,signal:AbortSignal.timeout(5e3)});r.ok,await r.text()}catch(t){let e=t instanceof Error?t.message:"Unknown error";e.includes("ENOTFOUND")||e.includes("ECONNRESET")}}getNextApiKey(){if(0===this.apiKeys.length)throw Error("No Browserless API keys available");let e=this.apiKeys[0],t=this.calculateKeyScore(e);for(let s of this.apiKeys){let r=this.calculateKeyScore(s);r<t&&(e=s,t=r)}return e}calculateKeyScore(e){return(this.keyUsageCount.get(e)||0)+10*(this.keyErrors.get(e)||0)}incrementKeyUsage(e){let t=this.keyUsageCount.get(e)||0;this.keyUsageCount.set(e,t+1)}incrementKeyError(e){let t=this.keyErrors.get(e)||0;this.keyErrors.set(e,t+1)}isKeyHealthy(e){return(this.keyErrors.get(e)||0)<this.ERROR_THRESHOLD}getHealthyKeys(){return this.apiKeys.filter(e=>this.isKeyHealthy(e))}async executeFunction(e,t,s){0===this.getHealthyKeys().length&&(this.keyErrors.clear(),this.apiKeys.forEach(e=>this.keyErrors.set(e,0)));let r=null;for(let o=0;o<this.MAX_RETRIES;o++)try{let r=this.getNextApiKey();return this.incrementKeyUsage(r),await this.makeRequest(r,e,t,s)}catch(e){if(r=e,this.isRateLimitError(e)){let e=this.getNextApiKey();this.incrementKeyError(e)}}throw r||Error("All Browserless API attempts failed")}async makeRequest(e,t,s,r){let o=`${this.ENDPOINT}/function?token=${e}`,a=s?{code:t,context:s}:t,i={"Content-Type":s?"application/json":"application/javascript","User-Agent":r?.userAgent||"RouKey-Browser-Agent/1.0"},l=await fetch(o,{method:"POST",headers:i,body:s?JSON.stringify(a):t,signal:AbortSignal.timeout(r?.timeout||3e4)});if(!l.ok){let e=await l.text();throw Error(`Browserless API error: ${l.status} - ${e}`)}return await l.json()}isRateLimitError(e){let t=e.message.toLowerCase();return t.includes("rate limit")||t.includes("quota")||t.includes("429")||t.includes("too many requests")}async navigateAndExtract(e,t){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });
        
        const title = await page.title();
        const content = ${t?`await page.$eval("${t}", el => el.textContent || el.innerText)`:"await page.evaluate(() => document.body.innerText)"};
        
        return {
          data: {
            url: "${e}",
            title,
            content: content?.trim() || ""
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}async executeBrowserQLAutomation(e,t={}){let{timeout:s=6e4,humanLike:r=!0,solveCaptcha:o=!0,sessionId:a,screenshots:i=!1}=t;if(0===this.getHealthyKeys().length)throw Error("No healthy Browserless API keys available");let l=null;for(let t=0;t<this.MAX_RETRIES;t++)try{let t=this.getNextApiKey(),r=`https://production-sfo.browserless.io/chromium/bql?token=${t}`;r+="&humanlike=true&adBlock=true&blockConsentModals=true&timeout=60000",a&&(r+=`&sessionId=${a}`);let o={query:e,variables:{}},i=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"RouKey-Browser-Automation/1.0"},body:JSON.stringify(o),signal:AbortSignal.timeout(s+1e4)});if(!i.ok){let e=await i.text();throw Error(`BrowserQL automation failed: ${i.status} ${i.statusText} - ${e}`)}let l=await i.json();if(l.errors&&l.errors.length>0)throw Error(`BrowserQL errors: ${l.errors.map(e=>e.message).join(", ")}`);return{data:l.data,type:"application/json",sessionId:l.sessionId,screenshots:l.screenshots}}catch(e){if(l=e,this.isRateLimitError(e)){let e=this.getNextApiKey();this.incrementKeyError(e)}}throw l||Error("All BrowserQL automation attempts failed")}async createBrowsingSession(e,t={}){let{timeout:s=3e5,humanLike:r=!0,blockResources:o=["*.png","*.jpg","*.gif","*.mp4","*.css"]}=t,a=`
      mutation CreateBrowsingSession {
        ${o.length>0?`
        reject(type: [image, media, stylesheet]) {
          enabled
          time
        }
        `:""}

        goto(url: "${e}", waitUntil: networkIdle) {
          status
          time
        }

        ${r?`
        waitForTimeout(time: ${Math.floor(2e3*Math.random())+1e3}) {
          time
        }
        `:""}

        reconnect(timeout: ${s}) {
          browserQLEndpoint
        }
      }
    `,i=await this.executeBrowserQLAutomation(a,{timeout:s});return{sessionId:i.sessionId||"default",reconnectUrl:i.data.reconnect.browserQLEndpoint}}async fillFormAdvanced(e,t,s){let r=`
      mutation FillFormAdvanced {
        ${t.map((e,t)=>{let s=e.delay||[50,150];return`
            field${t}: ${"select"===e.type?"select":"checkbox"===e.type||"radio"===e.type?"click":"type"}(
              selector: "${e.selector}"
              ${"checkbox"!==e.type&&"radio"!==e.type?`text: "${e.value}"`:""}
              ${"text"===e.type||"email"===e.type||"password"===e.type?`delay: [${s[0]}, ${s[1]}]`:""}
              visible: true
            ) {
              time
              ${"text"===e.type||"email"===e.type||"password"===e.type?"text":"x"}
            }
          `}).join("\n")}

        ${s?`
        submitForm: click(
          selector: "${s}"
          visible: true
        ) {
          time
          x
          y
        }
        `:""}

        # Wait for any page changes after form submission
        waitForTimeout(time: 2000) {
          time
        }
      }
    `;return await this.executeBrowserQLWithSession(e,r)}async executeBrowserQLWithSession(e,t){try{let s=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:t})});if(!s.ok)throw Error(`Session execution failed: ${s.status} ${s.statusText}`);let r=await s.json();if(r.errors&&r.errors.length>0)throw Error(`Session errors: ${r.errors.map(e=>e.message).join(", ")}`);return r}catch(e){throw e}}async searchAndExtractUnblocked(e,t="google"){let s="google"===t?`https://www.google.com/search?q=${encodeURIComponent(e)}`:`https://www.bing.com/search?q=${encodeURIComponent(e)}`;if(0===this.getHealthyKeys().length)throw Error("No healthy Browserless API keys available");let r=null;for(let o=0;o<this.MAX_RETRIES;o++)try{let r=this.getNextApiKey();this.incrementKeyUsage(r);let o=`${this.ENDPOINT}/unblock?token=${r}`,a={url:s,content:!0,browserWSEndpoint:!1,cookies:!1,screenshot:!1,waitForSelector:{selector:"h3, .g h3, .LC20lb, .b_algo h2",timeout:1e4}},i=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"RouKey-Browser-Agent/1.0"},body:JSON.stringify(a),signal:AbortSignal.timeout(6e4)});if(!i.ok){let e=await i.text();throw Error(`Browserless unblock API error: ${i.status} - ${e}`)}let l=await i.json();if(l.content){let r=this.parseSearchResults(l.content,t,e);return{data:{query:e,searchEngine:t,results:r,timestamp:new Date().toISOString(),debug:{pageTitle:"Unblocked search",pageUrl:s,totalElements:r.length,usedSelector:"unblock-api",extractedCount:r.length}},type:"application/json"}}throw Error("No content returned from unblock API")}catch(e){if(r=e,this.isRateLimitError(e)){let e=this.getNextApiKey();this.incrementKeyError(e)}}throw r||Error("All Browserless unblock API attempts failed")}async handleInfiniteScroll(e,t={}){let{maxScrolls:s=10,scrollDelay:r=2e3,contentSelector:o="body",stopCondition:a="no-more-content"}=t,i=`
      mutation HandleInfiniteScroll {
        # Get initial content count
        initialContent: text(selector: "${o}") {
          text
        }

        # Perform scrolling with content monitoring
        ${Array.from({length:s},(e,t)=>`
          scroll${t}: scroll(
            direction: down
            distance: 1000
          ) {
            time
          }

          waitAfterScroll${t}: waitForTimeout(time: ${r}) {
            time
          }

          contentCheck${t}: text(selector: "${o}") {
            text
          }
        `).join("\n")}

        # Get final content
        finalContent: text(selector: "${o}") {
          text
        }
      }
    `;return await this.executeBrowserQLWithSession(e,i)}async extractContentAdvanced(e,t){let s=`
      mutation ExtractContentAdvanced {
        ${t.map((e,t)=>"screenshot"===e.type?`
              ${e.name}: screenshot(
                selector: "${e.selector}"
                fullPage: false
              ) {
                data
              }
            `:"attribute"===e.type?`
              ${e.name}: html(
                selector: "${e.selector}"
                visible: true
              ) {
                html
              }
            `:`
              ${e.name}: ${e.type}(
                selector: "${e.selector}"
                visible: true
              ) {
                ${e.type}
              }
            `).join("\n")}
      }
    `;return await this.executeBrowserQLWithSession(e,s)}async executeComplexWorkflow(e,t){let s=await this.createBrowsingSession(e,{timeout:6e5,humanLike:!0}),r=this.generateEnhancedWorkflowScript(e,t);try{return{...await this.executeBrowserQLWithSession(s.reconnectUrl,r),sessionId:s.sessionId,reconnectUrl:s.reconnectUrl}}catch(e){throw e}}generateEnhancedWorkflowScript(e,t){return new Date().toISOString(),`
      mutation EnhancedBrowsingWorkflow {
        # Navigate to the target URL with enhanced options
        navigation: goto(url: "${e}", waitUntil: networkIdle) {
          status
          time
        }

        # Wait for initial page load
        waitForLoad: waitForTimeout(time: 3000) {
          time
        }

        # Get initial page state
        initialTitle: title {
          title
        }

        # Comprehensive popup handling - dismiss various types of popups
        ${this.generatePopupHandlingScript()}

        # Dynamic CAPTCHA detection and solving
        ${this.generateDynamicCaptchaScript()}

        # Execute workflow steps
        ${this.generateWorkflowSteps(t)}

        # Final page state
        finalTitle: title {
          title
        }

        finalUrl: url {
          url
        }

        pageContent: html {
          html
        }

        # Take screenshot for debugging
        screenshot: screenshot(encoding: base64, type: png) {
          data
        }
      }
    `}generatePopupHandlingScript(){return`
        # Handle cookie consent banners
        handleCookieConsent: if(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept'], [aria-label*='Accept'], .accept-cookies") {
          click(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept'], [aria-label*='Accept'], .accept-cookies", timeout: 5000) {
            time
          }
        }

        # Handle GDPR banners
        handleGDPRBanner: if(selector: "[class*='gdpr'], [id*='gdpr'], [data-testid*='gdpr'], .privacy-banner, .consent-banner") {
          click(selector: "[class*='gdpr'] button, [id*='gdpr'] button, [data-testid*='gdpr'] button, .privacy-banner button, .consent-banner button", timeout: 5000) {
            time
          }
        }

        # Handle modal close buttons
        handleModals: if(selector: ".modal-close, [aria-label='Close'], .close-button, [data-dismiss='modal'], .overlay-close") {
          click(selector: ".modal-close, [aria-label='Close'], .close-button, [data-dismiss='modal'], .overlay-close", timeout: 5000) {
            time
          }
        }

        # Handle newsletter popups
        handleNewsletterPopups: if(selector: "[class*='newsletter'] .close, [class*='popup'] .close, [class*='subscribe'] .close") {
          click(selector: "[class*='newsletter'] .close, [class*='popup'] .close, [class*='subscribe'] .close", timeout: 5000) {
            time
          }
        }

        # Wait after popup handling
        waitAfterPopups: waitForTimeout(time: 2000) {
          time
        }
    `}generateDynamicCaptchaScript(){return`
        # Check for Cloudflare Turnstile and solve if present
        handleCloudflare: if(selector: ".cf-turnstile, [data-sitekey], .cf-challenge") {
          verify(type: cloudflare, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Check for reCAPTCHA and solve if present
        handleRecaptcha: if(selector: ".g-recaptcha, [data-sitekey], .recaptcha, #recaptcha") {
          solve(type: recaptcha, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Check for hCAPTCHA and solve if present
        handleHcaptcha: if(selector: ".h-captcha, [data-sitekey*='hcaptcha'], .hcaptcha") {
          solve(type: hcaptcha, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Wait after potential CAPTCHA solving
        waitAfterCaptcha: waitForTimeout(time: 3000) {
          time
        }
    `}generateWorkflowSteps(e){return e.map((e,t)=>{let s=`step_${t}_${e.name}`;switch(e.type){case"navigate":return`
        ${s}: goto(url: "${e.params.url}", waitUntil: ${e.params.waitUntil||"networkIdle"}) {
          status
          time
        }`;case"click":if(e.params.optional)return`
        ${s}: if(selector: "${e.params.selector}") {
          click(selector: "${e.params.selector}", timeout: ${e.params.timeout||1e4}) {
            time
          }
        }`;return`
        ${s}: click(selector: "${e.params.selector}", timeout: ${e.params.timeout||1e4}) {
          time
        }`;case"type":return`
        ${s}: type(selector: "${e.params.selector}", text: "${e.params.text}") {
          time
        }`;case"wait":return`
        ${s}: waitForTimeout(time: ${e.params.time||1e3}) {
          time
        }`;case"waitForSelector":return`
        ${s}: waitForSelector(selector: "${e.params.selector}", timeout: ${e.params.timeout||1e4}) {
          time
        }`;case"extract":return`
        ${s}: html {
          html
        }`;case"scroll":return`
        ${s}: scroll(x: ${e.params.x||0}, y: ${e.params.y||500}) {
          time
        }`;case"screenshot":return`
        ${s}: screenshot(encoding: base64, type: png) {
          data
        }`;case"search":return`
        ${s}_search: type(selector: "${e.params.searchSelector||'input[type="search"], input[name="q"], input[name="query"]'}", text: "${e.params.query}") {
          time
        }
        ${s}_submit: click(selector: "${e.params.submitSelector||'button[type="submit"], input[type="submit"], .search-button'}", timeout: 10000) {
          time
        }`;case"form_fill":return e.params.fields.map((e,t)=>`
        ${s}_field_${t}: type(selector: "${e.selector}", text: "${e.value}") {
          time
        }`).join("");default:return`
        ${s}: waitForTimeout(time: 1000) {
          time
        }`}}).join("\n")}async performEnhancedWebSearch(e,t={}){let{maxSites:s=5,searchEngines:r=["google.com","bing.com","duckduckgo.com"],timeout:o=6e4,includeImages:a=!1}=t;try{let t=await this.executeBrowserQLAutomation(`
        mutation EnhancedWebSearch {
          # Search on Google first
          googleSearch: goto(url: "https://www.google.com/search?q=${encodeURIComponent(e)}", waitUntil: networkIdle) {
            status
            time
          }

          # Handle any popups or consent forms
          ${this.generatePopupHandlingScript()}
          ${this.generateDynamicCaptchaScript()}

          # Extract search results
          searchResults: html {
            html
          }

          # Take screenshot for debugging
          searchScreenshot: screenshot(encoding: base64, type: png) {
            data
          }
        }
      `,{timeout:o}),r=t.data?.searchResults?.html||"",a=this.extractSearchResultUrls(r,s),i=[];for(let t of a.slice(0,s))try{let s=await this.visitAndExtractSite(t.url,e);s.success&&i.push({url:t.url,title:t.title,content:s.content,relevanceScore:t.relevanceScore})}catch(e){}return{success:!0,query:e,discoveredSites:a.length,successfulSites:i.length,results:i,searchScreenshot:t.data?.searchScreenshot?.data}}catch(t){return{success:!1,error:t instanceof Error?t.message:String(t),query:e}}}extractSearchResultUrls(e,t=5){let s=[];try{let r,o=/<a[^>]+href="([^"]+)"[^>]*><h3[^>]*>([^<]+)<\/h3>/gi,a=0;for(;null!==(r=o.exec(e))&&a<t;){let e=r[1],o=r[2];if(!e.includes("google.com")&&!e.includes("googleadservices.com")&&!e.includes("googlesyndication.com")&&e.startsWith("http")){let r=(t-a)/t;s.push({url:decodeURIComponent(e),title:o.trim(),relevanceScore:r}),a++}}return s}catch(e){return[]}}async visitAndExtractSite(e,t){try{let t=await this.executeBrowserQLAutomation(`
        mutation VisitSite {
          # Navigate to the discovered site
          navigation: goto(url: "${e}", waitUntil: networkIdle) {
            status
            time
          }

          # Handle popups and consent forms
          ${this.generatePopupHandlingScript()}
          ${this.generateDynamicCaptchaScript()}

          # Wait for content to load
          waitForContent: waitForTimeout(time: 3000) {
            time
          }

          # Extract page title
          pageTitle: title {
            title
          }

          # Extract main content
          pageContent: html {
            html
          }

          # Get page URL (in case of redirects)
          finalUrl: url {
            url
          }
        }
      `,{timeout:3e4});if(!t.data?.pageContent?.html)return{success:!1,error:"No content extracted"};{let e=this.cleanExtractedContent(t.data.pageContent.html);return{success:!0,content:e}}}catch(e){return{success:!1,error:e instanceof Error?e.message:String(e)}}}cleanExtractedContent(e){try{let t=e.replace(/<script[^>]*>[\s\S]*?<\/script>/gi,"").replace(/<style[^>]*>[\s\S]*?<\/style>/gi,"").replace(/<nav[^>]*>[\s\S]*?<\/nav>/gi,"").replace(/<header[^>]*>[\s\S]*?<\/header>/gi,"").replace(/<footer[^>]*>[\s\S]*?<\/footer>/gi,"").replace(/<aside[^>]*>[\s\S]*?<\/aside>/gi,"").replace(/<!--[\s\S]*?-->/g,"").replace(/<[^>]+>/g," ").replace(/\s+/g," ").trim();return t.length>5e3&&(t=t.substring(0,5e3)+"..."),t}catch(t){return e.substring(0,1e3)}}parseSearchResults(e,t,s){let r=[];if("google"===t){let t=[/<h3[^>]*class="[^"]*LC20lb[^"]*"[^>]*>([^<]+)<\/h3>/i,/<h3[^>]*class="[^"]*DKV0Md[^"]*"[^>]*>([^<]+)<\/h3>/i,/<h3[^>]*>([^<]+)<\/h3>/i],o=[/<a[^>]*href="([^"]*)"[^>]*data-ved="[^"]*"[^>]*>/i,/<a[^>]*href="([^"]*)"[^>]*>/i],a=[/<span[^>]*class="[^"]*aCOpRe[^"]*"[^>]*>([^<]+)<\/span>/i,/<div[^>]*class="[^"]*VwiC3b[^"]*"[^>]*>([^<]+)<\/div>/i,/<span[^>]*class="[^"]*st[^"]*"[^>]*>([^<]+)<\/span>/i,/<div[^>]*data-content-feature="snippet"[^>]*>([^<]+)<\/div>/i,/<span[^>]*data-snippet="[^"]*"[^>]*>([^<]+)<\/span>/i,/<div[^>]*class="[^"]*IsZvec[^"]*"[^>]*>([^<]+)<\/div>/i,/<span[^>]*class="[^"]*hgKElc[^"]*"[^>]*>([^<]+)<\/span>/i];for(let i of[/<div[^>]*data-ved="[^"]*"[^>]*class="[^"]*g[^"]*"[^>]*>(.*?)<\/div>/gi,/<div[^>]*class="[^"]*MjjYud[^"]*"[^>]*>(.*?)<\/div>/gi,/<div[^>]*class="[^"]*kvH3mc[^"]*"[^>]*>(.*?)<\/div>/gi,/<div[^>]*class="[^"]*N54PNb[^"]*"[^>]*>(.*?)<\/div>/gi]){let l;for(;null!==(l=i.exec(e))&&r.length<10;){let e=l[1],i="";for(let s of t){let t=s.exec(e);if(t){i=t[1].trim();break}}let n="";for(let t of o){let s=t.exec(e);if(s){n=s[1];break}}if(i&&n&&n.startsWith("http")&&!n.includes("google.com")&&!n.includes("/search?")&&!n.includes("webcache.googleusercontent.com")){let t="";for(let s of a){let r=s.exec(e);if(r){t=(t=r[1].trim()).replace(/&[^;]+;/g," ").replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim();break}}if(!t){let s=e.replace(/<script[^>]*>.*?<\/script>/gi,"").replace(/<style[^>]*>.*?<\/style>/gi,"").replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim(),r=s.split(/[.!?]+/).filter(e=>e.trim().length>20);if(r.length>0)t=r[0].trim().substring(0,200)+"...";else{let e=s.split(" ").filter(e=>e.length>2);e.length>10&&(t=e.slice(0,25).join(" ")+"...")}}r.push({title:i,link:n,snippet:t||"No description available",searchEngine:"google",query:s,timestamp:new Date().toISOString(),relevanceScore:t.length>50?.9:.7})}}if(r.length>0)break}}else{let t=[/<h2[^>]*><a[^>]*href="([^"]*)"[^>]*>([^<]+)<\/a><\/h2>/i,/<a[^>]*href="([^"]*)"[^>]*><h2[^>]*>([^<]+)<\/h2><\/a>/i],o=[/<p[^>]*class="[^"]*b_lineclamp[^"]*"[^>]*>([^<]+)<\/p>/i,/<div[^>]*class="[^"]*b_caption[^"]*"[^>]*>([^<]+)<\/div>/i,/<p[^>]*>([^<]+)<\/p>/i];for(let a of[/<li[^>]*class="[^"]*b_algo[^"]*"[^>]*>(.*?)<\/li>/gi,/<div[^>]*class="[^"]*b_algoheader[^"]*"[^>]*>(.*?)<\/div>/gi]){let i;for(;null!==(i=a.exec(e))&&r.length<10;){let e=i[1],a="",l="";for(let s of t){let t=s.exec(e);if(t){l=t[1],a=t[2].trim();break}}if(a&&l){let t="";for(let s of o){let r=s.exec(e);if(r){t=r[1].trim().replace(/&[^;]+;/g," ").replace(/<[^>]*>/g," ").replace(/\s+/g," ").trim();break}}r.push({title:a,link:l,snippet:t||"No description available",searchEngine:"bing",query:s,timestamp:new Date().toISOString(),relevanceScore:t.length>50?.9:.7})}}if(r.length>0)break}}return r.sort((e,t)=>(t.relevanceScore||0)-(e.relevanceScore||0)),r.slice(0,8)}async searchAndExtract(e,t="google"){let s="google"===t?`https://www.google.com/search?q=${encodeURIComponent(e)}`:`https://www.bing.com/search?q=${encodeURIComponent(e)}`,r=`
      export default async function ({ page }) {
        // Set a realistic user agent and headers
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await page.setExtraHTTPHeaders({
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        });

        await page.goto("${s}", { waitUntil: 'networkidle0' });

        // Set up CAPTCHA solving
        const cdp = await page.createCDPSession();

        // Check for CAPTCHA and solve if found
        let captchaFound = false;
        cdp.on('Browserless.captchaFound', () => {
          console.log('CAPTCHA detected on search page');
          captchaFound = true;
        });

        // Wait a moment to see if CAPTCHA is detected
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (captchaFound) {
          console.log('Attempting to solve CAPTCHA...');
          try {
            const { solved, error } = await cdp.send('Browserless.solveCaptcha');
            console.log('CAPTCHA solving result:', { solved, error });

            if (solved) {
              console.log('CAPTCHA solved successfully');
              // Wait for page to reload after CAPTCHA solving
              await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 }).catch(() => {
                console.log('No navigation after CAPTCHA solve, continuing...');
              });
            } else {
              console.log('CAPTCHA solving failed:', error);
            }
          } catch (captchaError) {
            console.log('CAPTCHA solving error:', captchaError);
          }
        }

        // Wait for search results to load with multiple fallback selectors
        let resultsLoaded = false;
        const googleSelectors = ['[data-ved]', 'h3', '.g h3', '.LC20lb', '.DKV0Md', '#search h3'];
        const bingSelectors = ['.b_algo', '.b_algo h2', 'h2 a'];

        const selectorsToTry = '${t}' === 'google' ? googleSelectors : bingSelectors;

        for (const selector of selectorsToTry) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            resultsLoaded = true;
            console.log('Found results with selector:', selector);
            break;
          } catch (e) {
            console.log('Selector failed:', selector);
            continue;
          }
        }

        if (!resultsLoaded) {
          // Give it one more chance with a longer timeout on the most common selector
          try {
            await page.waitForSelector('${"google"===t?"h3":".b_algo"}', { timeout: 5000 });
          } catch (e) {
            console.log('All selectors failed, proceeding anyway...');
          }
        }

        const results = await page.evaluate(() => {
          console.log('Starting search results extraction...');

          // Try multiple selectors for extracting results
          const googleSelectors = [
            '[data-ved] h3',
            'h3',
            '.g h3',
            '.LC20lb',
            '.DKV0Md',
            '#search h3',
            '.yuRUbf h3',
            'a h3',
            '[role="heading"]'
          ];
          const bingSelectors = ['.b_algo h2', '.b_algo h2 a', 'h2 a'];

          const selectorsToTry = '${t}' === 'google' ? googleSelectors : bingSelectors;
          let elements = [];
          let usedSelector = '';

          // Debug: Log page content
          console.log('Page title:', document.title);
          console.log('Page URL:', window.location.href);
          console.log('Page body preview:', document.body.innerText.substring(0, 500));

          for (const selector of selectorsToTry) {
            elements = document.querySelectorAll(selector);
            console.log('Trying selector:', selector, 'found:', elements.length, 'elements');
            if (elements.length > 0) {
              usedSelector = selector;
              console.log('Found', elements.length, 'results with selector:', selector);
              break;
            }
          }

          // If no results found, try a more generic approach
          if (elements.length === 0) {
            console.log('No results with specific selectors, trying generic approach...');
            // Try to find any links that look like search results
            const allLinks = document.querySelectorAll('a[href*="/url?"]');
            console.log('Found', allLinks.length, 'Google result links');

            if (allLinks.length > 0) {
              elements = Array.from(allLinks).map(link => {
                const h3 = link.querySelector('h3');
                return h3 || link;
              }).filter(el => el && el.textContent?.trim());
              usedSelector = 'a[href*="/url?"] h3 (fallback)';
              console.log('Using fallback approach, found', elements.length, 'elements');
            }
          }

          const extractedResults = Array.from(elements).slice(0, 5).map(el => {
            const title = el.textContent?.trim() || '';
            let link = '';

            // Try to get the link
            if (el.href) {
              link = el.href;
            } else {
              const closestLink = el.closest('a');
              if (closestLink) {
                link = closestLink.href;
              }
            }

            return { title, link };
          }).filter(item => item.title && item.link);

          console.log('Final results:', extractedResults.length, 'items using selector:', usedSelector);

          return {
            results: extractedResults,
            debug: {
              pageTitle: document.title,
              pageUrl: window.location.href,
              totalElements: elements.length,
              usedSelector: usedSelector || 'none',
              extractedCount: extractedResults.length
            }
          };
        });

        return {
          data: {
            query: "${e}",
            searchEngine: "${t}",
            results: results.results,
            timestamp: new Date().toISOString(),
            debug: results.debug
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(r,null,{timeout:6e4})}async takeScreenshot(e,t){let s=t?.fullPage??!1,r=t?.selector||"",o=t?.quality||80,a=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        let screenshot;
        if ("${r}") {
          // Screenshot specific element
          const element = await page.waitForSelector("${r}", { timeout: 10000 });
          screenshot = await element.screenshot({
            encoding: 'base64',
            type: 'png'
          });
        } else {
          // Screenshot full page or viewport
          screenshot = await page.screenshot({
            encoding: 'base64',
            fullPage: ${s},
            type: 'png',
            quality: ${o}
          });
        }

        return {
          data: {
            url: "${e}",
            screenshot: screenshot,
            selector: "${r}",
            fullPage: ${s},
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(a)}async fillForm(e,t,s){let r=s?.submitAfterFill??!1,o=s?.waitForNavigation??!1,a=s?.formSelector||"form",i=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const formData = ${JSON.stringify(t)};
        const results = [];

        // Wait for form to be present
        await page.waitForSelector("${a}", { timeout: 10000 });

        // Fill each field intelligently
        for (const [fieldName, value] of Object.entries(formData)) {
          try {
            // Try multiple selector strategies
            const selectors = [
              \`input[name="\${fieldName}"]\`,
              \`input[id="\${fieldName}"]\`,
              \`textarea[name="\${fieldName}"]\`,
              \`select[name="\${fieldName}"]\`,
              \`input[placeholder*="\${fieldName}"]\`,
              \`input[aria-label*="\${fieldName}"]\`,
              \`[data-testid="\${fieldName}"]\`
            ];

            let filled = false;
            for (const selector of selectors) {
              const elements = await page.$$(selector);
              if (elements.length > 0) {
                const element = elements[0];
                const tagName = await element.evaluate(el => el.tagName.toLowerCase());

                if (tagName === 'select') {
                  await element.selectOption(value.toString());
                } else if (tagName === 'input') {
                  const inputType = await element.getAttribute('type');
                  if (inputType === 'checkbox' || inputType === 'radio') {
                    if (value) await element.check();
                  } else {
                    await element.fill(value.toString());
                  }
                } else {
                  await element.fill(value.toString());
                }

                results.push({
                  field: fieldName,
                  selector: selector,
                  value: value,
                  success: true
                });
                filled = true;
                break;
              }
            }

            if (!filled) {
              results.push({
                field: fieldName,
                value: value,
                success: false,
                error: 'Field not found'
              });
            }
          } catch (error) {
            results.push({
              field: fieldName,
              value: value,
              success: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }

        let submitResult = null;
        if (${r}) {
          try {
            const submitButton = await page.$('input[type="submit"], button[type="submit"], button:has-text("Submit")');
            if (submitButton) {
              ${o?"await Promise.all([page.waitForNavigation(), submitButton.click()]);":"await submitButton.click();"}
              submitResult = { success: true, message: 'Form submitted successfully' };
            } else {
              submitResult = { success: false, error: 'Submit button not found' };
            }
          } catch (error) {
            submitResult = { success: false, error: error instanceof Error ? error.message : String(error) };
          }
        }

        return {
          data: {
            url: "${e}",
            formFillResults: results,
            submitResult: submitResult,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(i)}async solveCaptcha(e,t="recaptcha"){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const captchaType = "${t}";
        let result = { success: false, type: captchaType };

        try {
          if (captchaType === 'recaptcha') {
            // Look for reCAPTCHA
            const recaptcha = await page.$('.g-recaptcha, [data-sitekey]');
            if (recaptcha) {
              // For now, we'll detect and report the presence
              // In production, integrate with 2captcha or similar service
              const sitekey = await recaptcha.getAttribute('data-sitekey');
              result = {
                success: false,
                type: 'recaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'reCAPTCHA detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'hcaptcha') {
            // Look for hCaptcha
            const hcaptcha = await page.$('.h-captcha, [data-hcaptcha-sitekey]');
            if (hcaptcha) {
              const sitekey = await hcaptcha.getAttribute('data-hcaptcha-sitekey');
              result = {
                success: false,
                type: 'hcaptcha',
                detected: true,
                sitekey: sitekey,
                message: 'hCaptcha detected but solving not implemented yet'
              };
            }
          } else if (captchaType === 'text') {
            // Look for text-based CAPTCHA
            const textCaptcha = await page.$('img[src*="captcha"], img[alt*="captcha"], .captcha-image');
            if (textCaptcha) {
              result = {
                success: false,
                type: 'text',
                detected: true,
                message: 'Text CAPTCHA detected but solving not implemented yet'
              };
            }
          }

          // If no CAPTCHA detected
          if (!result.detected) {
            result = {
              success: true,
              type: captchaType,
              detected: false,
              message: 'No CAPTCHA detected on page'
            };
          }
        } catch (error) {
          result = {
            success: false,
            type: captchaType,
            error: error instanceof Error ? error.message : String(error)
          };
        }

        return {
          data: {
            url: "${e}",
            captchaResult: result,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}async executeAdvancedScript(e,t,s){let r=s?.waitForSelector||"",o=s?.timeout||3e4,a=s?.returnType||"json",i=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        ${r?`await page.waitForSelector("${r}", { timeout: ${o} });`:""}

        // Execute custom script
        const scriptResult = await page.evaluate(() => {
          ${t}
        });

        let finalResult = scriptResult;

        if ("${a}" === 'screenshot') {
          const screenshot = await page.screenshot({
            encoding: 'base64',
            type: 'png'
          });
          finalResult = {
            scriptResult: scriptResult,
            screenshot: screenshot
          };
        }

        return {
          data: {
            url: "${e}",
            result: finalResult,
            returnType: "${a}",
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(i)}async smartExtract(e,t){let s=`
      export default async function ({ page }) {
        await page.goto("${e}", { waitUntil: 'networkidle0' });

        const goals = ${JSON.stringify(t)};
        const results = {};

        // Common extraction patterns
        const extractors = {
          prices: () => {
            const priceSelectors = [
              '[class*="price"]', '[id*="price"]', '.cost', '.amount',
              '[data-testid*="price"]', '.currency', '[class*="dollar"]'
            ];
            const prices = [];
            priceSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const text = el.textContent?.trim();
                if (text && /[$\xa3€\xa5₹]|\\d+\\.\\d{2}/.test(text)) {
                  prices.push({
                    text: text,
                    selector: selector,
                    element: el.tagName
                  });
                }
              });
            });
            return prices;
          },

          contact: () => {
            const contactSelectors = [
              '[href^="mailto:"]', '[href^="tel:"]', '.contact', '.email', '.phone'
            ];
            const contacts = [];
            contactSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                contacts.push({
                  text: el.textContent?.trim(),
                  href: el.getAttribute('href'),
                  type: el.getAttribute('href')?.startsWith('mailto:') ? 'email' : 'phone'
                });
              });
            });
            return contacts;
          },

          products: () => {
            const productSelectors = [
              '.product', '[class*="product"]', '.item', '[data-testid*="product"]'
            ];
            const products = [];
            productSelectors.forEach(selector => {
              document.querySelectorAll(selector).forEach(el => {
                const title = el.querySelector('h1, h2, h3, .title, [class*="title"]')?.textContent?.trim();
                const price = el.querySelector('[class*="price"], .cost')?.textContent?.trim();
                const image = el.querySelector('img')?.src;
                if (title) {
                  products.push({ title, price, image });
                }
              });
            });
            return products;
          },

          text: () => {
            // Extract main content
            const contentSelectors = ['main', 'article', '.content', '#content', '.post'];
            let content = '';
            for (const selector of contentSelectors) {
              const el = document.querySelector(selector);
              if (el) {
                content = el.textContent?.trim() || '';
                break;
              }
            }
            if (!content) {
              content = document.body.textContent?.trim() || '';
            }
            return content.substring(0, 5000); // Limit to 5000 chars
          },

          links: () => {
            const links = [];
            document.querySelectorAll('a[href]').forEach(el => {
              const href = el.getAttribute('href');
              const text = el.textContent?.trim();
              if (href && text && !href.startsWith('#')) {
                links.push({
                  url: new URL(href, window.location.href).href,
                  text: text
                });
              }
            });
            return links.slice(0, 50); // Limit to 50 links
          }
        };

        // Execute extractors based on goals
        goals.forEach(goal => {
          const goalLower = goal.toLowerCase();
          if (goalLower.includes('price') || goalLower.includes('cost')) {
            results.prices = extractors.prices();
          }
          if (goalLower.includes('contact') || goalLower.includes('email') || goalLower.includes('phone')) {
            results.contact = extractors.contact();
          }
          if (goalLower.includes('product') || goalLower.includes('item')) {
            results.products = extractors.products();
          }
          if (goalLower.includes('text') || goalLower.includes('content')) {
            results.text = extractors.text();
          }
          if (goalLower.includes('link') || goalLower.includes('url')) {
            results.links = extractors.links();
          }
        });

        // If no specific goals, extract everything
        if (goals.length === 0) {
          Object.keys(extractors).forEach(key => {
            results[key] = extractors[key]();
          });
        }

        return {
          data: {
            url: "${e}",
            extractionGoals: goals,
            results: results,
            timestamp: new Date().toISOString()
          },
          type: "application/json"
        };
      }
    `;return this.executeFunction(s)}getStats(){return{totalKeys:this.apiKeys.length,healthyKeys:this.getHealthyKeys().length,keyUsage:Object.fromEntries(this.keyUsageCount),keyErrors:Object.fromEntries(this.keyErrors)}}}let o=r}};