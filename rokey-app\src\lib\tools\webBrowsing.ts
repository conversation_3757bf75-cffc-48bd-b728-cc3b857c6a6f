// Client-side web browsing tool utilities
// This handles the interface between Manual Build nodes and the web browsing API

export interface WebBrowsingConfig {
  searchEngine?: 'google' | 'bing';
  extractionType?: 'content' | 'search' | 'screenshot' | 'custom';
  customSelector?: string;
  timeout?: number;
}

export interface WebBrowsingRequest {
  action: 'navigate' | 'search' | 'screenshot' | 'custom';
  url?: string;
  query?: string;
  searchEngine?: 'google' | 'bing';
  extractionType?: string;
  customSelector?: string;
  timeout?: number;
  code?: string;
  context?: any;
}

export interface WebBrowsingResponse {
  success: boolean;
  data: any;
  action: string;
  timestamp: string;
  error?: string;
  details?: string;
}

export class WebBrowsingTool {
  private static getApiEndpoint(): string {
    // In browser environment, use relative URL
    if (typeof window !== 'undefined') {
      return '/api/tools/web-browsing';
    }

    // In server environment (Node.js), construct full URL
    const baseUrl = process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    return `${baseUrl}/api/tools/web-browsing`;
  }

  static async navigate(url: string, config?: WebBrowsingConfig): Promise<WebBrowsingResponse> {
    const request: WebBrowsingRequest = {
      action: 'navigate',
      url,
      ...config
    };

    return this.makeRequest(request);
  }

  static async search(query: string, config?: WebBrowsingConfig): Promise<WebBrowsingResponse> {
    const request: WebBrowsingRequest = {
      action: 'search',
      query,
      searchEngine: config?.searchEngine || 'google',
      ...config
    };

    return this.makeRequest(request);
  }

  static async takeScreenshot(url: string, config?: WebBrowsingConfig): Promise<WebBrowsingResponse> {
    const request: WebBrowsingRequest = {
      action: 'screenshot',
      url,
      ...config
    };

    return this.makeRequest(request);
  }

  static async executeCustom(url: string, code: string, context?: any, config?: WebBrowsingConfig): Promise<WebBrowsingResponse> {
    const request: WebBrowsingRequest = {
      action: 'custom',
      url,
      code,
      context,
      ...config
    };

    return this.makeRequest(request);
  }

  static async getStatus(): Promise<any> {
    try {
      const response = await fetch(this.getApiEndpoint(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to get web browsing status:', error);
      throw error;
    }
  }

  private static async makeRequest(request: WebBrowsingRequest): Promise<WebBrowsingResponse> {
    try {
      const apiEndpoint = this.getApiEndpoint();
      console.log(`🌐 Making web browsing request to: ${apiEndpoint}`);

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.details || data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('Web browsing request failed:', error);
      throw error;
    }
  }

  // Helper method to execute web browsing based on Manual Build node configuration
  static async executeFromNodeConfig(nodeConfig: any, userInput: string): Promise<WebBrowsingResponse> {
    const { extractionType, searchEngine, customSelector, timeout } = nodeConfig;

    const config: WebBrowsingConfig = {
      searchEngine: searchEngine || 'google',
      extractionType: extractionType || 'content',
      customSelector,
      timeout: timeout || 30
    };

    // Determine action based on extraction type and user input
    if (extractionType === 'search') {
      return this.search(userInput, config);
    } else if (extractionType === 'screenshot') {
      // Assume user input is a URL for screenshots
      return this.takeScreenshot(userInput, config);
    } else if (extractionType === 'custom' && customSelector) {
      // Navigate to URL and extract using custom selector
      return this.navigate(userInput, config);
    } else {
      // Default: try to determine if input is URL or search query
      if (this.isUrl(userInput)) {
        return this.navigate(userInput, config);
      } else {
        return this.search(userInput, config);
      }
    }
  }

  private static isUrl(input: string): boolean {
    try {
      new URL(input);
      return true;
    } catch {
      return input.startsWith('http://') || input.startsWith('https://') || input.includes('.');
    }
  }

  // Generate example usage for different extraction types
  static getExampleUsage(extractionType: string): string {
    switch (extractionType) {
      case 'search':
        return 'Example: "latest iPhone price" or "weather in New York"';
      case 'screenshot':
        return 'Example: "https://example.com" (will take a screenshot)';
      case 'custom':
        return 'Example: "https://example.com" (will extract using custom selector)';
      default:
        return 'Example: "https://example.com" or "search query"';
    }
  }

  // Get human-readable description of what the tool will do
  static getActionDescription(nodeConfig: any): string {
    const { extractionType, searchEngine, customSelector } = nodeConfig;

    switch (extractionType) {
      case 'search':
        return `Search ${searchEngine || 'Google'} and return top results`;
      case 'screenshot':
        return 'Take a screenshot of the webpage';
      case 'custom':
        return `Extract content using selector: ${customSelector || 'custom selector'}`;
      default:
        return 'Navigate to webpage and extract content';
    }
  }
}
