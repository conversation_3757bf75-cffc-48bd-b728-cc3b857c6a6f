{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "inlZiMKWRoVFuIb1YGWKB", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "b2a10535acd7e2ae08110291427de3c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d74510f8cda8866c98bca7ac3a92daeeab8941c6421c69e98801848838839b12", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2f533f9b7b27dc1de2e1eb7bb7c063997f98ef08d70199f0d005223ed61a64e2"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "inlZiMKWRoVFuIb1YGWKB", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "SQeM/dRIuTzwzE+mn0ECQ9WSDQMS1SbO+0TNLdSRYhg=", "__NEXT_PREVIEW_MODE_ID": "b2a10535acd7e2ae08110291427de3c9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d74510f8cda8866c98bca7ac3a92daeeab8941c6421c69e98801848838839b12", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "2f533f9b7b27dc1de2e1eb7bb7c063997f98ef08d70199f0d005223ed61a64e2"}}}, "sortedMiddleware": ["/"]}